# Test Komodo Configuration File
[server]
host = "0.0.0.0"
port = 9120

[database]
type = "sqlite"
path = "/data/komodo.db"

[logging]
level = "info"
file = "/var/log/komodo.log"

[backup]
enabled = true
interval = "6h"
# Test backup: First backup - Sun 21 Sep 2025 13:01:59 IST
# Test backup: First backup - Sun 21 Sep 2025 13:03:36 IST
# Test backup: Second backup - Sun 21 Sep 2025 13:03:44 IST
# Test backup: Third backup - Sun 21 Sep 2025 13:03:53 IST
# Test backup: Fourth backup (rotation test) - Sun 21 Sep 2025 13:04:02 IST
# Test backup: Fifth backup (continued rotation) - Sun 21 Sep 2025 13:04:12 IST
# Test backup: First backup - Sun 21 Sep 2025 13:05:42 IST
# Test backup: Second backup - Sun 21 Sep 2025 13:05:55 IST
# Test backup: Third backup - Sun 21 Sep 2025 13:06:06 IST
# Test backup: Fourth backup (rotation test) - Sun 21 Sep 2025 13:06:15 IST
# Test backup: Fifth backup (continued rotation) - Sun 21 Sep 2025 13:06:26 IST
