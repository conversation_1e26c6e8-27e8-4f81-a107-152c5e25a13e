#!/bin/bash

# Build and push script for Manidae Backup Docker image
set -e

# Configuration
DOCKER_USERNAME="${DOCKER_USERNAME:-your-dockerhub-username}"
IMAGE_NAME="manidae-backup"
VERSION="${VERSION:-latest}"
FULL_IMAGE_NAME="$DOCKER_USERNAME/$IMAGE_NAME:$VERSION"

echo "=== Building and Pushing Manidae Backup Image ==="
echo "Image: $FULL_IMAGE_NAME"
echo ""

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Build the image
echo "🔨 Building Docker image..."
docker build --no-cache  -t "$FULL_IMAGE_NAME" .

if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully!"
else
    echo "❌ Failed to build Docker image"
    exit 1
fi

# Test the image
echo ""
echo "🧪 Testing the image..."
docker run --rm "$FULL_IMAGE_NAME" --help 2>/dev/null || echo "Image created successfully (help not available)"

# Login to Docker Hub (if not already logged in)
echo ""
echo "🔐 Checking Docker Hub authentication..."
if ! docker info | grep -q "Username:"; then
    echo "Please log in to Docker Hub:"
    docker login
fi

# Push the image
echo ""
echo "📤 Pushing image to Docker Hub..."
docker push "$FULL_IMAGE_NAME"

if [ $? -eq 0 ]; then
    echo "✅ Image pushed successfully!"
    echo ""
    echo "🎉 Your backup image is now available at:"
    echo "   docker pull $FULL_IMAGE_NAME"
    echo ""
    echo "📝 Update your docker-compose.prod.yml with:"
    echo "   image: $FULL_IMAGE_NAME"
else
    echo "❌ Failed to push image"
    exit 1
fi

echo ""
echo "=== Build and Push Complete ==="
