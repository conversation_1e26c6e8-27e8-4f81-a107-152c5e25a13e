#!/bin/bash

# Debug script to test SSH key processing
echo "=== SSH Key Debug Script ==="

# Test the SSH key from environment
if [ -z "$SSH_PRIVATE_KEY" ]; then
    echo "ERROR: SSH_PRIVATE_KEY environment variable not set"
    exit 1
fi

echo "SSH_PRIVATE_KEY length: ${#SSH_PRIVATE_KEY} characters"
echo "First 50 characters: ${SSH_PRIVATE_KEY:0:50}..."
echo "Last 50 characters: ...${SSH_PRIVATE_KEY: -50}"

# Test key processing logic
KEY_FILE="/tmp/test_ssh_key"

if [[ "$SSH_PRIVATE_KEY" == *"-----BEGIN"* ]]; then
    echo "Format detected: PEM with headers"
    echo "$SSH_PRIVATE_KEY" | sed 's/\\n/\n/g' > "$KEY_FILE"
else
    echo "Format detected: Raw key content"
    echo "-----BEGIN OPENSSH PRIVATE KEY-----" > "$KEY_FILE"
    echo "$SSH_PRIVATE_KEY" | fold -w 64 >> "$KEY_FILE"
    echo "-----END OPENSSH PRIVATE KEY-----" >> "$KEY_FILE"
fi

echo "=== Processed SSH Key ==="
echo "Key file size: $(wc -c < "$KEY_FILE") bytes"
echo "Key file lines: $(wc -l < "$KEY_FILE") lines"
echo "First 3 lines:"
head -3 "$KEY_FILE"
echo "Last 3 lines:"
tail -3 "$KEY_FILE"

# Test SSH key validity
echo "=== SSH Key Validation ==="
chmod 600 "$KEY_FILE"
ssh-keygen -l -f "$KEY_FILE" 2>&1 || echo "SSH key validation failed"

# Test GitHub connection
echo "=== GitHub SSH Test ==="
ssh -i "$KEY_FILE" -o StrictHostKeyChecking=no -T ************** 2>&1 | head -3

rm -f "$KEY_FILE"
echo "=== Debug Complete ==="
