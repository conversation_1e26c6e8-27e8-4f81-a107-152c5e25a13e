services:
  backup:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: manidae-backup
    env_file:
      - ./test/.env  # This will load SSH_PRIVATE_KEY and other variables
    environment:
      # Override/add specific backup configuration
      - REPO_URL=**************:ManidaeCloud/test-685112_syncresources.git
      - BACKUP_SOURCE_PATH=/etc/komodo
      - BACKUP_MODE=backup
      - GIT_USER_NAME=Backup Bot
      - GIT_USER_EMAIL=<EMAIL>
    volumes:
      # Mount the source directory to backup (for testing, we'll create a test folder)
      - ./test/komodo:/etc/komodo:ro
      # Mount work directory for persistence
      - backup_work:/work
    restart: "no"  # Run once and exit
    
  # For production use - runs as a scheduled job
  backup-scheduled:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: manidae-backup-scheduled
    env_file:
      - ./test/.env
    environment:
      - REPO_URL=**************:ManidaeCloud/test-685112_syncresources.git
      - BACKUP_SOURCE_PATH=/etc/komodo
      - BACKUP_MODE=backup
      - GIT_USER_NAME=Backup Bot
      - GIT_USER_EMAIL=<EMAIL>
    volumes:
      # In production, mount the actual /etc/komodo from the host
      - /etc/komodo:/etc/komodo:ro
      - backup_work:/work
    # Run backup every 6 hours
    command: >
      sh -c "while true; do 
        echo 'Starting scheduled backup at $(date)'; 
        /usr/local/bin/backup_script.sh; 
        echo 'Backup completed. Sleeping for 6 hours...'; 
        sleep 21600; 
      done"
    restart: unless-stopped
    profiles:
      - production

volumes:
  backup_work:
    driver: local
