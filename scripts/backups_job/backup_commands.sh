#!/bin/bash

# Helper script for backup operations
# Usage: ./backup_commands.sh [container_name] <command> [options]
#   or:  CONTAINER_NAME=my-backup ./backup_commands.sh <command> [options]

# Default container name
DEFAULT_CONTAINER_NAME="manidae-backup-job"

# Check if first argument looks like a container name (contains no spaces and isn't a known command)
KNOWN_COMMANDS="list restore backup status logs help"
if [ $# -ge 2 ] && [[ ! "$KNOWN_COMMANDS" =~ (^|[[:space:]])"$1"($|[[:space:]]) ]] && [[ "$1" != *" "* ]]; then
    # First argument is container name
    CONTAINER_NAME="$1"
    shift  # Remove container name from arguments
else
    # Use environment variable or default
    CONTAINER_NAME="${CONTAINER_NAME:-$DEFAULT_CONTAINER_NAME}"
fi

show_help() {
    echo "Manidae Backup Commands"
    echo ""
    echo "Usage: $0 [container_name] <command> [options]"
    echo "   or: CONTAINER_NAME=my-backup $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  list                    - List all available backup versions"
    echo "  restore <version>       - Restore specific backup version (1=latest, 2=second latest, etc.)"
    echo "  backup                  - Run manual backup now"
    echo "  status                  - Show backup service status"
    echo "  logs                    - Show backup service logs"
    echo "  help                    - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 list                           # List all backup versions (default container)"
    echo "  $0 my-backup-container list       # List backups from specific container"
    echo "  $0 restore 1                      # Restore latest backup"
    echo "  $0 my-backup-container restore 3  # Restore from specific container"
    echo "  CONTAINER_NAME=my-backup $0 backup # Run backup using environment variable"
    echo "  $0 status                         # Check if backup service is running"
    echo "  $0 logs                           # View recent backup logs"
    echo ""
    echo "Current container: $CONTAINER_NAME"
}

check_container() {
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        echo "❌ Container '$CONTAINER_NAME' is not running"
        echo "   Start it with: docker-compose -f docker-compose.prod.yml up -d"
        exit 1
    fi
}

case "${1:-help}" in
    list)
        echo "📋 Listing available backup versions..."
        check_container
        docker exec "$CONTAINER_NAME" env BACKUP_MODE=list /usr/local/bin/backup_script.sh
        ;;
    
    restore)
        if [ -z "$2" ]; then
            echo "❌ Error: Please specify version number to restore"
            echo "   Usage: $0 restore <version>"
            echo "   Example: $0 restore 1"
            exit 1
        fi
        
        echo "🔄 Restoring backup version $2..."
        echo "⚠️  This will replace current data with backup version $2"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            check_container
            docker exec "$CONTAINER_NAME" env BACKUP_MODE=restore RESTORE_VERSION="$2" /usr/local/bin/backup_script.sh
        else
            echo "Restore cancelled."
        fi
        ;;
    
    backup)
        echo "🔄 Running manual backup..."
        check_container
        docker exec "$CONTAINER_NAME" env BACKUP_MODE=backup /usr/local/bin/backup_script.sh
        ;;
    
    status)
        echo "📊 Backup service status:"
        if docker ps | grep -q "$CONTAINER_NAME"; then
            echo "✅ Container '$CONTAINER_NAME' is running"
            
            # Check if backup process is active
            if docker exec "$CONTAINER_NAME" pgrep -f "backup_script.sh" >/dev/null 2>&1; then
                echo "✅ Backup daemon is active"
            else
                echo "⚠️  Backup daemon may not be running"
            fi
            
            # Show container info
            echo ""
            echo "Container details:"
            docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        else
            echo "❌ Container '$CONTAINER_NAME' is not running"
        fi
        ;;
    
    logs)
        echo "📄 Recent backup logs:"
        check_container
        docker logs --tail 50 -f "$CONTAINER_NAME"
        ;;
    
    help|--help|-h)
        show_help
        ;;
    
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
