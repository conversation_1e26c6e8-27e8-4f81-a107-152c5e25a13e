FROM alpine:3.19

# Install required packages
RUN apk add --no-cache \
      git \
      bash \
      rsync \
      openssh-client \
      ca-certificates \
      coreutils \
      tzdata

# Create work directory
WORKDIR /work

# Copy and set up the backup script
COPY backup_script.sh /usr/local/bin/backup_script.sh
RUN chmod +x /usr/local/bin/backup_script.sh

# Set default command
CMD ["/usr/local/bin/backup_script.sh"]
