# Node modules will be installed in container
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files (will be mounted separately)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Runtime data
bandwidth-usage.json
*.log

# Development files
.git
.gitignore
README.md
*.md

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Test files
test/
tests/
*.test.js
*.spec.js

# Documentation
docs/
*.md

# Temporary files
tmp/
temp/
