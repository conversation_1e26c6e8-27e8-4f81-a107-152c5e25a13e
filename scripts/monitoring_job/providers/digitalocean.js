const axios = require("axios");

class DigitalOceanProvider {
    constructor(apiToken) {
        this.apiToken = apiToken;
        this.name = "DigitalOcean";
    }

    async getBandwidthUsage(dropletId) {
        try {
            // Get droplet details first to determine the plan
            const dropletRes = await axios.get(`https://api.digitalocean.com/v2/droplets/${dropletId}`, {
                headers: { Authorization: `Bearer ${this.apiToken}` }
            });

            const droplet = dropletRes.data.droplet;

            // DigitalOcean transfer allowances by plan (in GB)
            // Most basic droplets get 1TB (1000GB), but this varies by size
            const transferAllowanceGB = this.getTransferAllowance(droplet.size.slug);
            const includedBytes = transferAllowanceGB * (1024 ** 3);

            // Try to get bandwidth metrics for the last 24 hours
            const now = Math.floor(Date.now() / 1000);
            const oneDayAgo = now - (24 * 3600);

            let totalOutgoingBytes = 0;

            try {
                // Get outbound bandwidth metrics
                const outboundRes = await axios.get(
                    `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${dropletId}&interface=public&direction=outbound&start=${oneDayAgo}&end=${now}`,
                    {
                        headers: { Authorization: `Bearer ${this.apiToken}` },
                    }
                );

                // DigitalOcean API returns data in data.result format, not data.datapoints
                if (outboundRes.data && outboundRes.data.data && outboundRes.data.data.result) {
                    const results = outboundRes.data.data.result;

                    if (Array.isArray(results) && results.length > 0) {
                        // Each result contains values array with [timestamp, value] pairs
                        for (const result of results) {
                            if (result.values && Array.isArray(result.values)) {
                                for (const [timestamp, value] of result.values) {
                                    // Values are in bytes per second, integrate over time
                                    // Assuming 5-minute intervals (300 seconds)
                                    totalOutgoingBytes += parseFloat(value) * 300;
                                }
                            }
                        }
                    }
                }

                console.log(`DigitalOcean: Got bandwidth data for droplet ${dropletId}: ${(totalOutgoingBytes / (1024**3)).toFixed(3)} GB`);

            } catch (metricsError) {
                console.log(`DigitalOcean: Bandwidth metrics unavailable for droplet ${dropletId}:`, metricsError.response?.data?.message || metricsError.message);

                // For new droplets or when metrics are unavailable, return 0 usage
                if (metricsError.response?.status === 404 || metricsError.response?.status === 400) {
                    console.log(`DigitalOcean: Droplet ${dropletId} metrics not available yet, returning 0 usage`);
                    totalOutgoingBytes = 0;
                } else {
                    throw metricsError;
                }
            }

            return {
                outgoingBytes: totalOutgoingBytes,
                includedBytes: includedBytes
            };

        } catch (error) {
            console.error(`DigitalOcean: Failed to get bandwidth for Droplet ${dropletId}:`, error.response?.data?.message || error.message);
            return null;
        }
    }

    // Helper method to get transfer allowance based on droplet size
    getTransferAllowance(sizeSlug) {
        // DigitalOcean transfer allowances (in GB)
        const transferAllowances = {
            's-1vcpu-1gb': 1000,      // 1TB
            's-1vcpu-2gb': 2000,      // 2TB
            's-2vcpu-2gb': 3000,      // 3TB
            's-2vcpu-4gb': 4000,      // 4TB
            's-4vcpu-8gb': 5000,      // 5TB
            's-8vcpu-16gb': 6000,     // 6TB
            // Add more as needed
        };

        return transferAllowances[sizeSlug] || 1000; // Default to 1TB if unknown
    }

    async stopInstance(dropletId) {
        try {
            await axios.post(
                `https://api.digitalocean.com/v2/droplets/${dropletId}/actions`,
                { type: "shutdown" },
                {
                    headers: { Authorization: `Bearer ${this.apiToken}` },
                }
             );
            console.log(`DigitalOcean: Droplet ${dropletId} shut down.`);
            return true;
        } catch (error) {
            console.error(`DigitalOcean: Failed to shut down Droplet ${dropletId}:`, error.message);
            return false;
        }
    }

    async listServers() {
        if (!this.apiToken) {
            console.log("DigitalOcean: No API token configured, skipping");
            return [];
        }
        try {
            const res = await axios.get("https://api.digitalocean.com/v2/droplets", {
                headers: { Authorization: `Bearer ${this.apiToken}` },
            } );
            return res.data.droplets.map((d) => ({
                id: d.id,
                name: d.name,
                provider: this.name,
            }));
        } catch (error) {
            console.error("DigitalOcean: Failed to list Droplets:", error.message);
            return [];
        }
    }
}

module.exports = DigitalOceanProvider;
