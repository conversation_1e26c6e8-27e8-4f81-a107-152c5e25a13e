const axios = require("axios");

class HetznerProvider {
    constructor(apiToken) {
        this.apiToken = apiToken;
        this.name = "<PERSON><PERSON><PERSON>";
    }

    async getBandwidthUsage(serverId) {
        try {
            const res = await axios.get(`https://api.hetzner.cloud/v1/servers/${serverId}`,
                {
                    headers: { Authorization: `Bearer ${this.apiToken}` },
                }
             );
            const server = res.data.server;

            const outgoingBytes = server.outgoing_traffic || 0;
            const ingoingBytes = server.ingoing_traffic || 0;
            const includedBytes = server.included_traffic || 0;

            console.log(`Hetzner: Traffic data for server ${serverId}:`);
            console.log(`Hetzner: Outgoing: ${outgoingBytes} bytes (${(outgoingBytes / (1024**3)).toFixed(6)} GB)`);
            console.log(`Hetzner: Ingoing: ${ingoingBytes} bytes (${(ingoingBytes / (1024**3)).toFixed(6)} GB)`);
            console.log(`Hetzner: Included: ${includedBytes} bytes (${(includedBytes / (1024**3)).toFixed(6)} GB)`);

            // For bandwidth monitoring, we typically track outgoing traffic
            // But downloads (curl) show up as ingoing traffic
            // Use outgoing as primary, but note ingoing for reference
            const primaryBytes = outgoingBytes;

            if (ingoingBytes > 100 * 1024 * 1024) { // More than 100MB ingoing
                console.log(`Hetzner: Note: Large ingoing traffic detected (${(ingoingBytes / (1024**3)).toFixed(3)} GB) - likely downloads`);
            }

            return {
                outgoingBytes: primaryBytes,
                includedBytes: includedBytes,
                // Store ingoing for reference (downloads show up here)
                ingoingBytes: ingoingBytes
            };
        } catch (error) {
            console.error(`Hetzner: Failed to get bandwidth for server ${serverId}:`, error.message);
            return null;
        }
    }

    async stopInstance(serverId) {
        try {
            await axios.post(
                `https://api.hetzner.cloud/v1/servers/${serverId}/actions/shutdown`,
                {},
                {
                    headers: { Authorization: `Bearer ${this.apiToken}` },
                }
             );
            console.log(`Hetzner: Server ${serverId} shut down.`);
            return true;
        } catch (error) {
            console.error(`Hetzner: Failed to shut down server ${serverId}:`, error.message);
            return false;
        }
    }

    async listServers() {
        if (!this.apiToken) {
            console.log("Hetzner: No API token configured, skipping");
            return [];
        }
        try {
            const res = await axios.get("https://api.hetzner.cloud/v1/servers", {
                headers: { Authorization: `Bearer ${this.apiToken}` },
            } );
            return res.data.servers.map((s) => ({
                id: s.id,
                name: s.name,
                provider: this.name,
            }));
        } catch (error) {
            console.error("Hetzner: Failed to list servers:", error.message);
            return [];
        }
    }
}

module.exports = HetznerProvider;
