require("dotenv").config();

// Import provider modules
const HetznerProvider = require("./providers/hetzner");
const VultrProvider = require("./providers/vultr");
const LinodeProvider = require("./providers/linode");
const AWSLightsailProvider = require("./providers/aws-lightsail");
const AzureProvider = require("./providers/azure");
const GCPProvider = require("./providers/gcp");
const DigitalOceanProvider = require("./providers/digitalocean");

// Import utility modules
const NotificationService = require("./notification");
const { processServerData } = require("./utils");
const BandwidthTracker = require("./bandwidth-tracker");

// Configuration from Environment Variables
const HETZNER_API_TOKEN = process.env.HCLOUD_TOKEN;
const VULTR_API_TOKEN = process.env.VULTR_API_KEY;
const LINODE_API_TOKEN = process.env.LINODE_TOKEN;
const AWS_LIGHTSAIL_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID;
const AWS_LIGHTSAIL_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY;
const AWS_REGION = process.env.AWS_REGION || "us-east-1";
const AZURE_CLIENT_ID = process.env.AZURE_CLIENT_ID;
const AZURE_CLIENT_SECRET = process.env.AZURE_CLIENT_SECRET;
const AZURE_TENANT_ID = process.env.AZURE_TENANT_ID;
const AZURE_SUBSCRIPTION_ID = process.env.AZURE_SUBSCRIPTION_ID;
const GCP_PROJECT_ID = process.env.GCP_PROJECT_ID;
const GCP_CREDENTIALS_FILE = process.env.GCP_CREDENTIALS_FILE;
const DIGITALOCEAN_API_TOKEN = process.env.DIGITALOCEAN_TOKEN;

const DISCORD_BOT_TOKEN = process.env.DISCORD_BOT_TOKEN;
const DISCORD_CHANNEL_ID = process.env.DISCORD_CHANNEL_ID;

const NOTIFICATION_THRESHOLD_PERCENT = parseFloat(process.env.NOTIFICATION_THRESHOLD_PERCENT || "80");
const KILL_THRESHOLD_PERCENT = parseFloat(process.env.KILL_THRESHOLD_PERCENT || "95");
const SEND_USAGE_NOTIF_ALWAYS = process.env.SEND_USAGE_NOTIF_ALWAYS === "true";
const OBFUSCATE_SERVER_NAMES = process.env.OBFUSCATE_SERVER_NAMES === "true";

// Initialize providers
const providers = [
    new HetznerProvider(HETZNER_API_TOKEN),
    new VultrProvider(VULTR_API_TOKEN),
    new LinodeProvider(LINODE_API_TOKEN),
    new AWSLightsailProvider(AWS_LIGHTSAIL_ACCESS_KEY_ID, AWS_LIGHTSAIL_SECRET_ACCESS_KEY, AWS_REGION),
    new AzureProvider(AZURE_CLIENT_ID, AZURE_CLIENT_SECRET, AZURE_TENANT_ID, AZURE_SUBSCRIPTION_ID),
    new GCPProvider(GCP_PROJECT_ID, GCP_CREDENTIALS_FILE),
    new DigitalOceanProvider(DIGITALOCEAN_API_TOKEN),
];

// Initialize notification service and bandwidth tracker
const notificationService = new NotificationService(DISCORD_BOT_TOKEN, DISCORD_CHANNEL_ID);
const bandwidthTracker = new BandwidthTracker();

async function monitorProvider(provider) {
    console.log(`Fetching ${provider.name} servers...`);
    const servers = await provider.listServers();
    const processedServers = [];

    for (const server of servers) {
        let usage;
        // Handle special cases for providers that need extra parameters
        if (provider.name === "GCP") {
            usage = await provider.getBandwidthUsage(server.id, server.zone, server.numericId);
        } else if (provider.name === "Lightsail") {
            usage = await provider.getBandwidthUsage(server.id, server.region);
        } else {
            usage = await provider.getBandwidthUsage(server.id);
        }

        if (usage) {
            // Convert bytes to GB for bandwidth tracker
            const currentUsageGB = usage.outgoingBytes / (1024 ** 3);

            // Analyze usage with advanced tracking
            const analysis = bandwidthTracker.analyzeUsage(
                provider.name,
                server.id,
                server.name,
                currentUsageGB
            );

            // Create server data with analysis results
            const serverData = {
                id: server.id,
                name: server.name,
                provider: provider.name,
                zone: server.zone, // For GCP
                numericId: server.numericId, // For GCP
                currentUsageGB: currentUsageGB,
                analysis: analysis,
                // Legacy fields for compatibility
                outgoingTB: (usage.outgoingBytes / (1024 ** 4)).toFixed(4),
                limitTB: (usage.includedBytes / (1024 ** 4)).toFixed(4),
                usagePercentage: usage.includedBytes ? ((usage.outgoingBytes / usage.includedBytes) * 100).toFixed(2) + "%" : "0.00%",
                rawPercentage: usage.includedBytes ? (usage.outgoingBytes / usage.includedBytes) * 100 : 0,
            };

            // Determine action based on analysis
            if (analysis.action === 'terminate') {
                serverData.action = 'kill';
            } else if (analysis.action === 'warn' || analysis.warnings.length > 0) {
                serverData.action = 'notify';
            } else {
                serverData.action = 'none';
            }

            processedServers.push(serverData);

            // Log detailed analysis
            console.log(`\n=== ${provider.name}: ${server.name} Analysis ===`);
            console.log(`Current usage: ${currentUsageGB.toFixed(2)} GB`);
            console.log(`Daily burst limit: ${analysis.dailyBurstLimitGB} GB`);
            console.log(`Monthly limit: ${analysis.monthlyLimitGB} GB`);
            console.log(`Action: ${analysis.action}`);
            console.log(`Reason: ${analysis.reason}`);
            if (analysis.warnings.length > 0) {
                console.log(`Warnings: ${analysis.warnings.join(', ')}`);
            }
            console.log(`Termination enabled: ${analysis.enableTermination}`);
        }
    }

    return processedServers;
}

async function executeShutdowns(serversToKill, providers) {
    const killedServers = [];
    const enableTermination = process.env.ENABLE_INSTANCE_TERMINATION === "true";

    for (const server of serversToKill) {
        const provider = providers.find(p => p.name === server.provider);
        if (!provider) continue;

        console.log(`\n🚨 TERMINATION TRIGGERED for ${server.provider}: ${server.name}`);
        console.log(`Reason: ${server.analysis.reason}`);
        console.log(`Current usage: ${server.currentUsageGB.toFixed(2)} GB`);

        if (!enableTermination) {
            console.log(`⚠️  TERMINATION DISABLED - Would have terminated ${server.name}`);
            console.log(`   Set ENABLE_INSTANCE_TERMINATION=true to actually terminate instances`);
            continue;
        }

        let shutdownSuccess = false;

        console.log(`🔥 ACTUALLY TERMINATING ${server.name}...`);

        // Handle GCP special case (needs zone)
        if (server.provider === "GCP") {
            shutdownSuccess = await provider.stopInstance(server.id, server.zone);
        } else {
            shutdownSuccess = await provider.stopInstance(server.id);
        }

        if (shutdownSuccess) {
            killedServers.push(server);
            console.log(`✅ Successfully terminated ${server.name}`);
        } else {
            console.log(`❌ Failed to terminate ${server.name}`);
        }
    }

    return killedServers;
}

function generateNotificationMessage(serversToKill, serversToNotify, allServersData) {
    let message = "";
    let notificationType = "info";
    const enableTermination = process.env.ENABLE_INSTANCE_TERMINATION === "true";

    if (serversToKill.length > 0) {
        notificationType = "kill";
        message += `🚨 ${serversToKill.length} server(s) triggered TERMINATION threshold! 🚨\n\n`;
        for (const server of serversToKill) {
            message += `- ${server.provider}: ${server.name}\n`;
            message += `  Current usage: ${server.currentUsageGB.toFixed(2)} GB\n`;
            message += `  Reason: ${server.analysis.reason}\n`;
            message += `  Action: ${enableTermination ? 'TERMINATED' : 'WOULD TERMINATE (disabled)'}\n\n`;
        }
    }

    if (serversToNotify.length > 0) {
        if (notificationType !== "kill") {
            notificationType = "alert";
        }
        message += `⚠️ ${serversToNotify.length} server(s) triggered WARNING threshold: ⚠️\n\n`;
        for (const server of serversToNotify) {
            message += `- ${server.provider}: ${server.name}\n`;
            message += `  Current usage: ${server.currentUsageGB.toFixed(2)} GB\n`;
            if (server.analysis.warnings.length > 0) {
                message += `  Warnings: ${server.analysis.warnings.join(', ')}\n`;
            }
            message += `  Reason: ${server.analysis.reason}\n\n`;
        }
    }

    if (SEND_USAGE_NOTIF_ALWAYS || (serversToNotify.length > 0 || serversToKill.length > 0)) {
        if (allServersData.length > 0) {
            message += "--- Current Usage Summary ---\n";
            for (const server of allServersData) {
                const report = bandwidthTracker.getUsageReport(server.provider, server.id);
                if (report) {
                    message += `- ${server.provider}: ${server.name}\n`;
                    message += `  Current: ${server.currentUsageGB.toFixed(2)} GB\n`;
                    message += `  Monthly: ${report.monthlyUsageGB.toFixed(2)} GB / ${report.monthlyLimitGB} GB\n`;
                    message += `  Projected: ${report.projectedMonthlyUsage.toFixed(2)} GB\n`;
                    message += `  Burst events: ${report.burstEvents}\n\n`;
                } else {
                    message += `- ${server.provider}: ${server.name} - ${server.currentUsageGB.toFixed(2)} GB\n`;
                }
            }
        } else {
            message += "No servers found or monitored.\n";
        }
    }

    return { message, notificationType };
}

// Main execution
(async () => {
    const allServersData = [];
    const serversToNotify = [];
    const serversToKill = [];

    console.log("Starting bandwidth monitoring...");

    // Monitor all providers
    for (const provider of providers) {
        try {
            const providerServers = await monitorProvider(provider);
            allServersData.push(...providerServers);

            // Categorize servers by action needed
            for (const server of providerServers) {
                if (server.action === 'kill') {
                    serversToKill.push(server);
                } else if (server.action === 'notify') {
                    serversToNotify.push(server);
                }
            }
        } catch (error) {
            console.error(`Error monitoring ${provider.name}:`, error.message);
        }
    }

    // Execute shutdowns if needed
    if (serversToKill.length > 0) {
        await executeShutdowns(serversToKill, providers);
    }

    // Send notifications
    const { message, notificationType } = generateNotificationMessage(serversToKill, serversToNotify, allServersData);

    if (message && (SEND_USAGE_NOTIF_ALWAYS || serversToNotify.length > 0 || serversToKill.length > 0)) {
        await notificationService.send(message, notificationType);
    } else {
        console.log("No thresholds exceeded and SEND_USAGE_NOTIF_ALWAYS is false. No notification sent.");
    }

    console.log("Bandwidth monitoring complete.");

    // Clean up Discord client
    await notificationService.destroy();
})();
