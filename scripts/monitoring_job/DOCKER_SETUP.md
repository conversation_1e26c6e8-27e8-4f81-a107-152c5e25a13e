# Docker Bandwidth Monitor Setup Guide

This guide explains how to deploy the bandwidth monitoring system using Docker.

## 🚀 Quick Start

### 1. Setup Environment
```bash
cd scripts
./setup-docker.sh
```

### 2. Configure Credentials
Edit `scripts/.env.docker` with your API credentials:
```bash
# Example configuration
HCLOUD_TOKEN=your_hetzner_token
VULTR_API_KEY=your_vultr_key
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_CHANNEL_ID=your_channel_id

# Advanced monitoring settings
MONTHLY_BANDWIDTH_LIMIT_GB=20480
DAILY_BURST_LIMIT_GB=1000
EARLY_WARNING_THRESHOLD_GB=500
ENABLE_INSTANCE_TERMINATION=false  # Set to true for production
```

### 3. Start the Service
```bash
# Start all services including bandwidth monitor
docker compose up -d

# Or start only the bandwidth monitor
docker compose up -d bandwidth_monitor
```

## 📊 Monitoring Commands

### View Logs
```bash
# Follow bandwidth monitor logs
docker compose logs -f bandwidth_monitor

# View recent logs
docker compose logs --tail=50 bandwidth_monitor
```

### Test Configuration
```bash
# Test the container
docker compose run --rm bandwidth_monitor echo "Test successful"

# Run a single monitoring cycle
docker compose run --rm bandwidth_monitor node monitor.js
```

### Manage Service
```bash
# Restart the monitor
docker compose restart bandwidth_monitor

# Stop the monitor
docker compose stop bandwidth_monitor

# View service status
docker compose ps bandwidth_monitor
```

## 🔧 Configuration

### Environment Variables
The monitoring service uses `scripts/.env.docker` for configuration:

- **Cloud Provider APIs**: Configure only the providers you use
- **Discord Notifications**: Set bot token and channel ID
- **Monitoring Thresholds**: Customize bandwidth limits and warnings
- **Safety Settings**: Control whether termination is enabled

### Data Persistence
- **Bandwidth Data**: Stored in Docker volume `bandwidth_data`
- **GCP Credentials**: Mounted from `scripts/gcp-keys/` (read-only)
- **Configuration**: Loaded from `scripts/.env.docker`

### Monitoring Frequency
- **Default**: Every hour (3600 seconds)
- **Customizable**: Edit the `sleep` value in `docker-compose.yml`

## 🛡️ Security Features

- **Isolated Container**: Separate Node.js environment
- **Non-root User**: Runs as user `monitor` (UID 1001)
- **Read-only Mounts**: Credentials mounted read-only
- **Health Checks**: Automatic container health monitoring
- **Separate Config**: API credentials isolated from frontend

## 🔍 Troubleshooting

### Common Issues

**Container won't start:**
```bash
# Check logs for errors
docker compose logs bandwidth_monitor

# Verify configuration
docker compose config
```

**Discord notifications not working:**
```bash
# Test Discord configuration
docker compose run --rm bandwidth_monitor node -e "
const { Client, GatewayIntentBits } = require('discord.js');
console.log('Discord.js loaded successfully');
"
```

**No cloud instances found:**
```bash
# Verify API credentials
docker compose run --rm bandwidth_monitor node -e "
require('dotenv').config();
console.log('Tokens configured:', {
  hetzner: !!process.env.HCLOUD_TOKEN,
  vultr: !!process.env.VULTR_API_KEY,
  gcp: !!process.env.GCP_PROJECT_ID
});
"
```

### Performance Optimization

**Reduce build time:**
- The `.dockerignore` file excludes unnecessary files
- Dependencies are cached between builds
- Use `docker compose build --no-cache` for clean builds

**Monitor resource usage:**
```bash
# View container resource usage
docker stats bandwidth_monitor

# View container details
docker inspect manidae-cloud-bandwidth_monitor
```

## 📈 Production Deployment

### Before Going Live

1. **Test thoroughly** with `ENABLE_INSTANCE_TERMINATION=false`
2. **Set realistic thresholds** based on your usage patterns
3. **Configure Discord notifications** and test them
4. **Set up monitoring** of the monitoring service itself
5. **Enable termination** with `ENABLE_INSTANCE_TERMINATION=true`

### Recommended Production Settings
```bash
MONTHLY_BANDWIDTH_LIMIT_GB=20480      # 20TB
DAILY_BURST_LIMIT_GB=1000             # 1TB daily burst
BURST_WINDOW_HOURS=24                 # 24 hour window
EARLY_WARNING_THRESHOLD_GB=500        # 500GB warning
ENABLE_INSTANCE_TERMINATION=true      # Enable termination
```

### Monitoring the Monitor
- Set up external monitoring of the Docker container
- Monitor Discord notifications to ensure they're working
- Regularly check logs for any errors or issues
- Consider setting up alerts if the container stops

## 🔄 Updates and Maintenance

### Updating the Monitor
```bash
# Rebuild and restart
docker compose build bandwidth_monitor
docker compose up -d bandwidth_monitor

# Or use the build script
./build_images.sh
```

### Backup Configuration
```bash
# Backup your configuration
cp scripts/.env.docker scripts/.env.docker.backup

# Backup bandwidth data
docker run --rm -v bandwidth_data:/data -v $(pwd):/backup alpine tar czf /backup/bandwidth-data-backup.tar.gz -C /data .
```

This Docker setup provides a robust, secure, and maintainable way to run your bandwidth monitoring system! 🚀
