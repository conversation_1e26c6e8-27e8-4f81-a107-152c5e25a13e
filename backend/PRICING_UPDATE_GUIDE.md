# Pricing Update Guide

This guide explains how to update pricing data without destroying the database or restarting the application.

## Overview

The pricing system now supports updating existing entries and adding new ones without clearing all data. This allows administrators to:

- Change pricing for existing cloud providers/instances
- Activate/deactivate specific pricing entries
- Add new cloud providers, regions, or instance types
- Update support costs and package multipliers

## How to Update Pricing

### Step 1: Edit the CSV File

Edit `backend/config/pricing.csv` with your changes:

- **Change prices**: Modify the `hourly_cost` column
- **Activate/Deactivate**: Change the `active` column (1 = active, 0 = inactive)
- **Add new entries**: Add new rows with all required columns
- **Update multipliers**: Modify package multiplier or support cost columns

### Step 2: Run the Update Script

```bash
cd backend
source venv/bin/activate
PYTHONPATH=/home/<USER>/Projects/manidae-cloud/backend python3 app/db/load_pricing_data.py
```

The script will:
- Compare CSV data with existing database entries
- Update only the entries that have changed
- Add new entries that don't exist in the database
- Show a summary of what was updated/added

### Step 3: Reload Pricing in the Application

You have two options:

#### Option A: Restart the uvicorn server (recommended)
```bash
# Stop the server (Ctrl+C) and restart
python3 -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Option B: Use the API endpoint (requires admin access)
```bash
curl -X POST "http://localhost:8000/api/deployments/pricing/reload" \
     -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
     -H "X-API-Key: YOUR_API_KEY"
```

## Script Options

### Update Mode (Default)
```bash
python3 app/db/load_pricing_data.py
# or explicitly
python3 app/db/load_pricing_data.py --update
```
- Updates existing entries where values have changed
- Adds new entries from CSV
- Preserves existing database entries not in CSV

### Clear Mode (Legacy)
```bash
python3 app/db/load_pricing_data.py --clear
```
- Clears ALL existing pricing data
- Reloads everything from CSV
- Use only if you want to completely reset pricing data

## Examples

### Example 1: Deactivate Azure
1. Edit CSV: Change Azure entry from `active=1` to `active=0`
2. Run: `python3 app/db/load_pricing_data.py`
3. Restart uvicorn
4. Result: Azure will no longer appear in cloud provider options

### Example 2: Update Google Cloud Pricing
1. Edit CSV: Change `Google Cloud,e2-micro,us-central1,0.012,...` to `Google Cloud,e2-micro,us-central1,0.015,...`
2. Run: `python3 app/db/load_pricing_data.py`
3. Restart uvicorn
4. Result: New pricing will be used for calculations

### Example 3: Add New Provider
1. Edit CSV: Add new row `NewProvider,instance-1,region-1,0.05,1.0,1.5,2.0,0,0.0685,0.274,1`
2. Run: `python3 app/db/load_pricing_data.py`
3. Restart uvicorn
4. Result: NewProvider will appear in cloud provider options

## Important Notes

- **Backup**: Always backup your database before making changes
- **Testing**: Test changes in a development environment first
- **BYOVPS**: BYOVPS entries are excluded from "Create Deployment" but still work for "Bring Your Own Server"
- **Active Status**: Only active entries (active=1) are shown to users
- **Restart Required**: The uvicorn server needs to be restarted to pick up database changes (or use the admin API endpoint)

## Troubleshooting

### Script Errors
- Ensure the virtual environment is activated
- Check that PYTHONPATH is set correctly
- Verify CSV format matches the expected columns

### Changes Not Visible
- Restart the uvicorn server
- Check that the database was actually updated
- Verify the active status is set to 1

### Database Issues
- Use `--clear` mode to completely reset if needed
- Check database connection settings
- Ensure proper permissions for database operations
