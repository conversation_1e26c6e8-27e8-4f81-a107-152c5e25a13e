# Manidae Cloud Backend API

This document provides an overview of the Manidae Cloud Backend API, including initial setup, user management, and deployment operations.

## Initial Setup

### First Time Setup Process

1. **Configure Environment**: Set up your `.env` file with database and cloud provider credentials (see Configuration section below)

2. **Start Database**: Run the PostgreSQL database using Docker:
   ```bash
   docker-compose up -d db
   ```

3. **Initialize Database Schema**: Run the database initialization script to create tables and load pricing data:
   ```bash
   python -m app.db.init_db
   ```

4. **Create First Administrator**: 
   - Start the backend server: `uvicorn app.main:app --reload`
   - Navigate to the frontend application
   - Sign up with your administrator credentials - **the first user to register automatically becomes an administrator**
   - All subsequent users will be regular users and can be promoted to admin by existing administrators

### User Management

The system implements role-based access control with two user types:

- **Administrators**: Can view all deployments, manage all users, create/delete users, and assign admin roles
- **Regular Users**: Can only view and manage their own deployments and account

**Important**: The very first user to register becomes an administrator automatically. After that, only administrators can promote other users to admin status through the user management interface.

## API Endpoints

The API provides endpoints for managing deployments of cloud instances.

### 1. Create a Deployment (POST)

Deploys a new instance on the specified cloud provider with the given configuration.

```bash
curl -X POST "http://127.0.0.1:8000/api/deployments/" \
     -H "Content-Type: application/json" \
     -d '{
           "package": "Pangolin",
           "cloud_provider": "Google Cloud",
           "region": "us-central1",
           "instance_type": "e2-medium",
           "support_level": "Standard"
         }'
```

### 2. Get All Deployments (GET)

Retrieves deployments based on user role:
- **Administrators**: See all deployments from all users
- **Regular Users**: See only their own deployments

```bash
curl "http://127.0.0.1:8000/api/deployments/" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "X-API-Key: YOUR_API_KEY"
```

### 3. Get Deployments by User ID (GET) - Admin Only

**Admin Only Endpoint**: Retrieves a list of deployments for a specific user ID. Only administrators can access this endpoint.

```bash
curl "http://127.0.0.1:8000/api/deployments/user/{user_id}" \
     -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
     -H "X-API-Key: YOUR_API_KEY"
# Example:
curl "http://127.0.0.1:8000/api/deployments/user/3" \
     -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
     -H "X-API-Key: YOUR_API_KEY"
```

### 4. Update a Deployment (PATCH)

Updates an existing deployment. This can be used to change instance types, which will trigger a Terraform apply.

```bash
curl -X PATCH "http://127.0.0.1:8000/api/deployments/{deployment_id}" \
     -H "Content-Type: application/json" \
     -d '{"instance_type": "e2-micro"}'
# Example:
curl -X PATCH "http://127.0.0.1:8000/api/deployments/4" \
     -H "Content-Type: application/json" \
     -d '{"instance_type": "e2-micro"}'
```

### 5. Delete a Deployment (DELETE)

Initiates the destruction of a deployed instance and soft-deletes its record in the database. The deployment directory will also be removed.

```bash
curl -X DELETE "http://127.0.0.1:8000/api/deployments/{deployment_id}"
# Example:
curl -X DELETE "http://127.0.0.1:8000/api/deployments/2"
```

## Configuration

To run the backend, you need to set the following environment variables. Create a `.env` file in the backend directory by copying `.env-copy` and populating it with your specific values.

Example `.env-copy` file:
```
GCP_PROJECT_ID=your_gcp_project_id
DATABASE_URL=postgresql://user:password@host:port/database_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name
```

**Note:** The `.env` file is ignored by Git to prevent sensitive information from being committed.

## Running the Database (Docker)

To run the PostgreSQL database using Docker Compose, ensure you have Docker installed and your `.env` file is configured with the `DB_USER`, `DB_PASSWORD`, and `DB_NAME` variables. Then, navigate to the `backend` directory and run:

```bash
docker-compose up -d db
```

This will start the PostgreSQL container in the background, making it accessible at `localhost:5432`.

## Database Management

### Database Schema Initialization

To initialize the database schema and load pricing data, run the `init_db.py` script. This sets up all required tables and loads cloud pricing information:

```bash
python -m app.db.init_db
```

**Note**: This script does not create any user accounts. The first user to register through the application will automatically become an administrator.

## Terraform Komodo Provider workaround
Prior to inclusion in the official registry we can use a workaround to get the custom provider to work.

build the provider
```bash
cd manidae-cloud/submodules/terraform-provider-komodo
go mod tidy # download dependencies
go build -o ./bin/terraform-provider-komodo-provider
```

```bash
vi ~/.terraformrc
provider_installation {
    dev_overrides {
        "example.com/me/komodo-provider" = "/home/<USER>/Projects/manidae-cloud/submodules/terraform-provider-komodo/bin/"
    }
    direct {} # For all other providers, install directly from their origin provider.
}
```

### Pricing Data

The system uses a CSV file to store pricing information for different cloud providers, instance types, and regions. This data is used to calculate deployment costs.

#### CSV File Location

The pricing data is stored in a CSV file located at:
- `backend/app/config/pricing.csv`

#### Loading Pricing Data

To load or update the pricing data in the database from the CSV file, run:

```bash
python -m app.db.load_pricing_data
```

This will clear existing pricing data in the database and load the latest data from the CSV file.

#### CSV Format

The pricing CSV file should have the following columns:
- `cloud_provider`: The name of the cloud provider (e.g., "Google Cloud", "AWS", "Azure")
- `instance_type`: The type of instance (e.g., "e2-medium", "t3.micro")
- `region`: The region where the instance is deployed (e.g., "us-central1", "us-east-1")
- `hourly_cost`: The base hourly cost for the instance
- `package_multiplier_pangolin`: The cost multiplier for the Pangolin package
- `package_multiplier_premium`: The cost multiplier for the Premium package
- `support_cost_level1`: Additional hourly cost for Level 1 support
- `support_cost_level2`: Additional hourly cost for Level 2 support
- `support_cost_level3`: Additional hourly cost for Level 3 support

## Security

### Authentication and Authorization

The system implements comprehensive security measures:

-   **JWT Authentication:** All API endpoints require valid JWT tokens for user authentication
-   **Role-Based Access Control (RBAC):** 
    - **Administrators** can view all deployments, manage users, and perform system-wide operations
    - **Regular Users** can only access their own deployments and account settings
-   **API Key Protection:** All endpoints require a valid API key header (`X-API-Key`)
-   **Resource Ownership:** Users can only access resources they own (except administrators who have full access)

### Security Features

- **Secure User Creation:** No default accounts - the first registered user becomes admin
- **Password Security:** All passwords are hashed using argon2
- **Token-Based Sessions:** Secure JWT token management with configurable expiration
- **Protected Endpoints:** All sensitive operations require proper authentication and authorization
- **Deployment Isolation:** Regular users cannot view or modify deployments owned by other users

### Production Readiness

The authentication and authorization system is designed for production use. Ensure you:

1. Set strong `SECRET_KEY` and `API_SECRET_KEY` values in your environment
2. Use secure password policies for user registration
3. Configure appropriate token expiration times
4. Enable HTTPS in production environments
5. Regularly audit user permissions and access patterns

## Provider Architecture and Extensibility

The application is designed with an extensible provider architecture, allowing for easy integration of new cloud providers. The core logic for handling provider-specific Terraform generation is encapsulated within strategy classes.

### Class Hierarchy

```
BaseProviderStrategy
├── GoogleCloudStrategy
├── HetznerStrategy
├── LinodeStrategy
└── VultrStrategy
```

-   `BaseProviderStrategy`: An abstract base class defining the interface for all cloud provider strategies. It ensures that all concrete provider implementations adhere to a common contract, primarily the `generate_terraform_files` method.
-   `GoogleCloudStrategy`, `HetznerStrategy`, `LinodeStrategy`, `VultrStrategy`: Concrete implementations of `BaseProviderStrategy`, each containing the specific logic and templates required to generate Terraform configurations for their respective cloud platforms.

### Adding a New Cloud Provider

To add support for a new cloud provider, follow these steps:

1.  **Create a New Provider Strategy File**:
    -   In the `app/core/deployment/providers/` directory, create a new Python file (e.g., `aws.py` for AWS).

2.  **Implement the New Strategy Class**:
    -   Inside the new file, define a class that inherits from `BaseProviderStrategy`.
    -   Implement the `generate_terraform_files(self, deployment_dir: str, deployment: Deployment)` method. This method will contain the logic to:
        -   Generate `main.tf`, `variables.tf`, `outputs.tf`, `terraform.tfvars`, `startup-script.sh`, and `config-template.toml` files specific to the new cloud provider.
        -   Utilize Jinja2 templates (if applicable) for dynamic content generation.
        -   Handle any provider-specific configurations or API interactions.

    ```python
    # Example: app/core/deployment/providers/aws.py
    from app.core.deployment.providers.base import BaseProviderStrategy
    from app.models.deployment import Deployment

    class AwsStrategy(BaseProviderStrategy):
        def generate_terraform_files(self, deployment_dir: str, deployment: Deployment):
            # Your AWS-specific Terraform generation logic here
            pass
    ```

3.  **Register the New Provider**:
    -   Open `app/core/deployment/deployment_manager.py`.
    -   Import your new strategy class.
    -   Add an entry to the `self.provider_strategies` dictionary in the `DeploymentManager` class, mapping the cloud provider's name (as it will appear in the API) to an instance of your new strategy class.

    ```python
    # Example: app/core/deployment/deployment_manager.py
    from app.core.deployment.providers.aws import AwsStrategy
    # ... other imports

    class DeploymentManager:
        def __init__(self):
            self.provider_strategies = {
                "Google Cloud": GoogleCloudStrategy(),
                "Hetzner": HetznerStrategy(),
                "Linode": LinodeStrategy(),
                "Vultr": VultrStrategy(),
                "AWS": AwsStrategy(),  # Add your new provider here
            }
    ```

By following these steps, the new cloud provider will be integrated into the deployment system, and the `DeploymentManager` will be able to correctly route deployment requests to the appropriate provider-specific logic.

### Backup SSH Certs (Saving acme.json when Deleting VPS)
If you are deleting your server you run the risk of deleting ssh certs and letsencrypt rate limiting you. It is recommended that you back up your ssh certs
from the `conf/letsencrypt/acme.json` file. You can do this using this script

```bash
FILENAME="acme.json"
TOKEN="REPLACE_WITH_YOUR_GITHUB_TOKEN"
CONTENT=$(<"$FILENAME")

PAYLOAD=$(jq -n \
  --arg file "$FILENAME" \
  --arg content "$CONTENT" \
  '{
    description: "Uploaded from VPS",
    public: false,
    files: {
      ($file): { content: $content }
    }
  }'
)

curl -X POST https://api.github.com/gists \
  -H "Authorization: token $TOKEN" \
  -H "Accept: application/vnd.github+json" \
  -d "$PAYLOAD"
```

you can then retrive the acme.json file later using. You will need the raw url from your gist. You can find this on your gist.

```bash

TOKEN="your_github_token_here"

curl -H "Authorization: token $TOKEN" -L -o acme.json "https://gist.githubusercontent.com/.../raw/acme.json"

```