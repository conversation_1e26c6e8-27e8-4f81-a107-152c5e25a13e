#!/usr/bin/env python3
"""
Show summary of data in the database
"""

import psycopg2
from datetime import datetime

# Database connection
DB_PARAMS = {
    'host': 'localhost',
    'port': 5432,
    'database': 'manidae',
    'user': 'postgres',
    'password': 'mysecretpassword'
}

def show_summary():
    """Show summary of all test data"""
    try:
        conn = psycopg2.connect(**DB_PARAMS)
        cursor = conn.cursor()
        
        print("📊 DATABASE DATA SUMMARY")
        print("=" * 50)
        
        # Users summary
        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_admin = true")
        admin_users = cursor.fetchone()[0]
        print(f"👥 USERS: {total_users} total ({admin_users} admins, {total_users - admin_users} regular)")
        
        # Deployments summary
        cursor.execute("SELECT COUNT(*) FROM deployments")
        total_deployments = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM deployments WHERE deleted_at IS NULL")
        active_deployments = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM deployments WHERE deleted_at IS NOT NULL")
        deleted_deployments = cursor.fetchone()[0]
        print(f"🚀 DEPLOYMENTS: {total_deployments} total ({active_deployments} active, {deleted_deployments} deleted)")
        
        # Deployments by package
        cursor.execute("""
            SELECT package, COUNT(*) 
            FROM deployments 
            GROUP BY package 
            ORDER BY COUNT(*) DESC
        """)
        packages = cursor.fetchall()
        print("   Packages:")
        for package, count in packages:
            print(f"     - {package}: {count}")
        
        # Transactions summary
        cursor.execute("SELECT COUNT(*) FROM transactions")
        total_transactions = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM transactions WHERE amount < 0")
        charges = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM transactions WHERE amount > 0")
        credits = cursor.fetchone()[0]
        print(f"💰 TRANSACTIONS: {total_transactions} total ({charges} charges, {credits} credits)")
        
        # Transaction date range
        cursor.execute("SELECT MIN(created_at), MAX(created_at) FROM transactions")
        min_date, max_date = cursor.fetchone()
        if min_date and max_date:
            print(f"   Date range: {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}")
        
        # Referral credits summary
        cursor.execute("SELECT COUNT(*) FROM referral_credits")
        total_referrals = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM referral_credits WHERE is_active = true")
        active_referrals = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM referral_credits WHERE used_amount > 0")
        used_referrals = cursor.fetchone()[0]
        print(f"🎁 REFERRAL CREDITS: {total_referrals} total ({active_referrals} active, {used_referrals} partially used)")
        
        # Referral credits by type
        cursor.execute("""
            SELECT credit_type, COUNT(*) 
            FROM referral_credits 
            GROUP BY credit_type
        """)
        credit_types = cursor.fetchall()
        print("   Types:")
        for credit_type, count in credit_types:
            print(f"     - {credit_type}: {count}")
        
        # IP address analysis for referrals
        cursor.execute("""
            SELECT ip_address, COUNT(*) as count
            FROM referral_credits 
            WHERE ip_address IS NOT NULL
            GROUP BY ip_address 
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC
            LIMIT 5
        """)
        duplicate_ips = cursor.fetchall()
        if duplicate_ips:
            print("   🚨 Potential abuse (duplicate IPs):")
            for ip, count in duplicate_ips:
                print(f"     - {ip}: {count} credits")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    show_summary()
