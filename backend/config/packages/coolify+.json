{"package": "Coolify+", "package_description": "Self host your applications with enhanced security using Crowdsec", "non_compatible_packages": ["Coolify", "Pangolin", "Pangolin+", "Pangolin+AI"], "components": ["coolify+", "crowdsec"], "details": [{"name": "coolify+", "required_env": [{"name": "DOMAIN", "hint": "Your domain name (e.g., example.com)"}], "optional_env": [{"name": "ADMIN_SUBDOMAIN", "hint": "Subdomain for admin access (e.g., coolify)"}], "description": "Self hosted apps like vercel and netlify with enhanced features"}, {"name": "crowdsec", "required_env": [{"name": "CROWDSEC_ENROLLMENT_KEY", "hint": "CrowdSec enrollment key for security protection"}], "description": "Crowd sourced security protection"}]}