#!/bin/bash

# Usage: ./toggle_komodo_provider.sh <deployment_dir> <comment|uncomment>

DEPLOYMENT_DIR="$1"
ACTION="$2"

if [ $# -ne 2 ]; then
    echo "Usage: $0 <deployment_dir> <comment|uncomment>"
    exit 1
fi

MAIN_TF="$DEPLOYMENT_DIR/main.tf"

if [ ! -f "$MAIN_TF" ]; then
    echo "Error: $MAIN_TF not found"
    exit 1
fi

if [ "$ACTION" = "comment" ]; then
    echo "Commenting komodo-provider blocks..."

    python3 - <<EOF
import re

with open("$MAIN_TF") as f:
    lines = f.readlines()

output = []
in_block = False
brace_balance = 0

# Patterns to match block headers
patterns = [
    r'^\s*komodo-provider\s*=\s*{',
    r'^\s*provider\s+"komodo-provider"\s*{',
    r'^\s*resource\s+"komodo-provider_user"\s+".*?"\s*{'
]

def is_block_start(line):
    return any(re.match(p, line) for p in patterns)

for line in lines:
    if not in_block and is_block_start(line) and not line.strip().startswith("#"):
        in_block = True
        brace_balance = line.count("{") - line.count("}")
        output.append("# " + line.rstrip())
        continue

    if in_block:
        brace_balance += line.count("{") - line.count("}")
        output.append("# " + line.rstrip())
        if brace_balance <= 0:
            in_block = False
        continue

    output.append(line.rstrip())

with open("$MAIN_TF", "w") as f:
    f.write("\n".join(output) + "\n")
EOF

elif [ "$ACTION" = "uncomment" ]; then
    echo "Uncommenting komodo-provider blocks..."

    python3 - <<EOF
import re

with open("$MAIN_TF") as f:
    lines = f.readlines()

output = []
in_block = False
brace_balance = 0

# Patterns to detect commented block starts
patterns = [
    r'^\s*#\s*komodo-provider\s*=\s*{',
    r'^\s*#\s*provider\s+"komodo-provider"\s*{',
    r'^\s*#\s*resource\s+"komodo-provider_user"\s+".*?"\s*{'
]

def is_commented_block_start(line):
    return any(re.match(p, line) for p in patterns)

for line in lines:
    if not in_block and is_commented_block_start(line):
        in_block = True
        brace_balance = line.count("{") - line.count("}")
        output.append(re.sub(r'^\s*#\s?', '', line.rstrip()))
        continue

    if in_block:
        brace_balance += line.count("{") - line.count("}")
        output.append(re.sub(r'^\s*#\s?', '', line.rstrip()))
        if brace_balance <= 0:
            in_block = False
        continue

    output.append(line.rstrip())

with open("$MAIN_TF", "w") as f:
    f.write("\n".join(output) + "\n")
EOF

else
    echo "Error: Action must be 'comment' or 'uncomment'"
    exit 1
fi

echo "Done!"
