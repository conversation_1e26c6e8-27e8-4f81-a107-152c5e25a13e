from app.core.pricing.pricing_service import PricingService


def test_support_only_pricing_for_vps():
    service = PricingService()
    result = service.calculate_pricing(
        cloud_provider=None,
        instance_type=None,
        region=None,
        package='Pangolin',
        support_level='Level 2',
        server_type='vps'
    )
    assert result['breakdown']['instance_cost'] == 0.0
    assert result['breakdown']['support_cost'] > 0.0
    assert 'byovps_management_fee' in result['breakdown']
    assert result['breakdown']['byovps_management_fee'] > 0.0
    # Total cost should be support + BYOVPS fee
    expected_cost = result['breakdown']['support_cost'] + result['breakdown']['byovps_management_fee']
    assert abs(result['hourly_cost'] - expected_cost) < 0.0001  # Allow for floating point precision


def test_support_only_pricing_for_existing():
    service = PricingService()
    result = service.calculate_pricing(
        cloud_provider=None,
        instance_type=None,
        region=None,
        package='Premium',
        support_level='Level 3',
        server_type='existing'
    )
    assert result['breakdown']['instance_cost'] == 0.0
    assert result['breakdown']['support_cost'] > 0.0
    assert 'byovps_management_fee' in result['breakdown']
    assert result['breakdown']['byovps_management_fee'] > 0.0
    # Total cost should be support + BYOVPS fee (existing servers also get BYOVPS fee)
    expected_cost = result['breakdown']['support_cost'] + result['breakdown']['byovps_management_fee']
    assert abs(result['hourly_cost'] - expected_cost) < 0.0001  # Allow for floating point precision


def test_byovps_fee_configuration():
    """Test that BYOVPS fee is correctly configured from environment"""
    from app.core.config import get_settings
    service = PricingService()
    settings = get_settings()

    result = service.calculate_pricing(
        cloud_provider=None,
        instance_type=None,
        region=None,
        package='Pangolin',
        support_level='Level 1',
        server_type='vps'
    )

    # BYOVPS fee should be daily fee / 24 hours
    expected_hourly_fee = settings.BILLING_BYOVPS_DAILY_FEE / 24
    assert abs(result['breakdown']['byovps_management_fee'] - expected_hourly_fee) < 0.0001

