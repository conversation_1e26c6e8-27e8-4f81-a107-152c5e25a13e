import pytest

try:
    from fastapi.testclient import TestClient
    from app.main import app
    client = TestClient(app)
except Exception:
    client = None


def test_servers_endpoints_exist():
    if client is None:
        pytest.skip("httpx not installed; skipping server endpoint tests")
    # endpoints should exist and require API key; since none is provided, 403 or 401 expected
    r = client.get('/api/servers/')
    assert r.status_code in (401, 403)

    r = client.get('/api/servers/periphery-script')
    assert r.status_code in (401, 403)

    r = client.post('/api/servers/validate-vps', json={'ip_address': '*******'})
    assert r.status_code in (401, 403)

