# Deployment Manager Tests

This directory contains focused tests for the deployment manager functionality, specifically testing terraform configuration generation and validation for all cloud providers.

## Test Structure

### Core Test Files

1. **`test_terraform_generation.py`** - Focused tests for terraform file generation
   - Tests that all 6 required files are generated correctly for each provider
   - Tests that terraform init passes for all providers
   - Tests that Premium package configurations work
   - Tests terraform plan validation (partial)
   - Runs actual terraform commands for validation

2. **`conftest.py`** - Pytest configuration and shared fixtures
   - Provides common test fixtures
   - Configures test markers
   - Sets up environment variables

## Running Tests

### Prerequisites

1. **Install pytest and dependencies:**
   ```bash
   pip install pytest pytest-mock paramiko python-terraform pydantic-settings
   ```

2. **Install terraform (for integration tests):**
   ```bash
   # On Ubuntu/Debian
   curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add -
   sudo apt-add-repository "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main"
   sudo apt-get update && sudo apt-get install terraform
   ```

### Running Different Test Categories

#### Run all tests:
```bash
cd backend
python -m pytest tests/ -v
```

#### Run only terraform tests:
```bash
python -m pytest -m terraform -v -s
```

#### Run only fast file generation tests:
```bash
python -m pytest tests/test_terraform_generation.py::TestTerraformGeneration::test_terraform_file_generation -v
```

#### Run only terraform init validation tests:
```bash
python -m pytest tests/test_terraform_generation.py::TestTerraformGeneration::test_terraform_init_validation -v
```

#### Skip slow tests:
```bash
python -m pytest -m "not slow" -v
```

### Test Status

✅ **Fully Working Tests (13/16 passing):**

**File Generation Tests (4/4 passing):**
- `test_terraform_file_generation[Google Cloud]` - Tests GCP file generation
- `test_terraform_file_generation[Hetzner]` - Tests Hetzner file generation
- `test_terraform_file_generation[Linode]` - Tests Linode file generation
- `test_terraform_file_generation[Vultr]` - Tests Vultr file generation

**Terraform Init Validation (4/4 passing):**
- `test_terraform_init_validation[Google Cloud]` - Tests terraform init for GCP
- `test_terraform_init_validation[Hetzner]` - Tests terraform init for Hetzner
- `test_terraform_init_validation[Linode]` - Tests terraform init for Linode
- `test_terraform_init_validation[Vultr]` - Tests terraform init for Vultr

**Premium Package Tests (4/4 passing):**
- `test_premium_package_generation[Google Cloud]` - Tests Premium package for GCP
- `test_premium_package_generation[Hetzner]` - Tests Premium package for Hetzner
- `test_premium_package_generation[Linode]` - Tests Premium package for Linode
- `test_premium_package_generation[Vultr]` - Tests Premium package for Vultr

**Terraform Plan Validation (1/4 passing):**
- `test_terraform_plan_validation[Google Cloud]` ✅ - Tests terraform plan for GCP

⚠️ **Partially Working (3/16 failing):**
- `test_terraform_plan_validation[Hetzner]` - Fails due to missing variable values
- `test_terraform_plan_validation[Linode]` - Fails due to missing variable values
- `test_terraform_plan_validation[Vultr]` - Fails due to missing variable values

## Generated Files Location

### Where Test Files Are Created

During test execution, the deployment manager creates terraform files in the following location:

```
backend/deployments/{deployment_id}/
├── main.tf              # Provider-specific terraform configuration
├── variables.tf         # Variable definitions
├── outputs.tf          # Output definitions
├── terraform.tfvars    # Variable values
├── startup-script.sh   # VM initialization script
├── config-template.toml # Application configuration
└── .terraform/         # Terraform state (after terraform init)
```

### Test vs Production Deployment Folders

**Test Deployments:**
- Tests use deployment ID `999` by default
- Created in temporary directories during test execution
- Located at: `backend/deployments/999/` (in temp directory)
- Files are automatically cleaned up after tests complete

**Production Deployments:**
- Real deployments use auto-generated sequential IDs (1, 2, 3, etc.)
- Created in the actual backend directory
- Located at: `backend/deployments/{actual_id}/`
- Files persist for actual infrastructure management

### Examining Generated Files

**Option 1: Run tests and examine files before cleanup**
```bash
# Run a single test with verbose output to see the files being created
python -m pytest tests/test_terraform_generation.py::TestTerraformGeneration::test_terraform_file_generation[Google\ Cloud] -v -s

# The test will show you the temporary directory path where files are created
```

**Option 2: Look at existing production deployments**
```bash
# Check what deployments already exist
ls -la backend/deployments/

# Example: Look at deployment 1 (Google Cloud)
ls -la backend/deployments/1/
cat backend/deployments/1/main.tf
cat backend/deployments/1/terraform.tfvars

# Example: Look at deployment 11 (Linode)
ls -la backend/deployments/11/
cat backend/deployments/11/main.tf
cat backend/deployments/11/terraform.tfvars
```

**Option 3: Create a deployment manually for testing (Recommended)**

Create deployments for each provider (run from root folder):

**Google Cloud:**
```bash
cd backend

python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.gcp import GoogleCloudStrategy

deployment = MockDeployment('Google Cloud')
deployment.id = 'test-gcp'

os.environ.update({
    'GCP_PROJECT_ID': 'test-project-123',
    'GCP_CREDENTIALS_FILE': '/tmp/test-creds.json',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Google Cloud strategy and call generate_terraform_files on it
gcp_strategy = GoogleCloudStrategy()
gcp_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Google Cloud files created in: {deployment_dir}')
"
```

**Hetzner:**
```bash
python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.hetzner import HetznerStrategy

deployment = MockDeployment('Hetzner')
deployment.id = 'test-hetzner'

os.environ.update({
    'HCLOUD_TOKEN': 'test-hetzner-token--12345678901234567890123456789012345678901234',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Hetzner strategy and call generate_terraform_files on it
hetzner_strategy = HetznerStrategy()
hetzner_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Hetzner files created in: {deployment_dir}')
"
```

**Linode:**
```bash
python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.linode import LinodeStrategy

deployment = MockDeployment('Linode')
deployment.id = 'test-linode'

os.environ.update({
    'LINODE_TOKEN': 'test-linode-token',
    'LINODE_ROOT_PASSWORD': 'test-root-password',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Linode strategy and call generate_terraform_files on it
linode_strategy = LinodeStrategy()
linode_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Linode files created in: {deployment_dir}')
"
```

**Vultr:**
```bash
python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.vultr import VultrStrategy

deployment = MockDeployment('Vultr')
deployment.id = 'test-vultr'

os.environ.update({
    'VULTR_API_KEY': 'test-vultr-key',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Vultr strategy and call generate_terraform_files on it
vultr_strategy = VultrStrategy()
vultr_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Vultr files created in: {deployment_dir}')
"
```

**Premium Package Example (any provider):**
```bash
python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.gcp import GoogleCloudStrategy

deployment = MockDeployment('Google Cloud', package='Premium')
deployment.id = 'test-gcp-premium'

os.environ.update({
    'GCP_PROJECT_ID': 'test-project-123',
    'GCP_CREDENTIALS_FILE': '/tmp/test-creds.json',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token',
    'CROWDSEC_ENROLLMENT_KEY': 'test-crowdsec-key'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Google Cloud strategy and call generate_terraform_files on it
gcp_strategy = GoogleCloudStrategy()
gcp_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Premium Google Cloud files created in: {deployment_dir}')
"
```

**Testing the generated files:**
```bash
# Comment out komodo-provider sections for terraform init
./tests/toggle_komodo_provider.sh backend/deployments/test-gcp comment

# Run terraform init
cd backend/deployments/test-gcp
terraform init

# Uncomment komodo-provider sections for terraform plan (if needed)
cd ../../..
./tests/toggle_komodo_provider.sh backend/deployments/test-gcp uncomment

# Run terraform plan
cd backend/deployments/test-gcp
terraform plan

# Clean up
cd ../../..
rm -rf backend/deployments/test-*
```

### File Structure by Provider

Each provider generates slightly different terraform configurations:

**Google Cloud (`backend/deployments/{id}/`):**
- `main.tf` - Contains `google_compute_instance` and `google_compute_firewall` resources
- `variables.tf` - Contains GCP-specific variables like `gcp_project_id`, `gcp_region`, `gcp_zone`
- `terraform.tfvars` - Contains actual values for GCP project, credentials, etc.

**Hetzner (`backend/deployments/{id}/`):**
- `main.tf` - Contains `hcloud_server` and `hcloud_firewall` resources  
- `variables.tf` - Contains Hetzner-specific variables like `hcloud_token`, `location`
- `terraform.tfvars` - Contains actual values for Hetzner token, location, etc.

**Linode (`backend/deployments/{id}/`):**
- `main.tf` - Contains `linode_instance` and `linode_firewall` resources
- `variables.tf` - Contains Linode-specific variables like `linode_token`, `root_password`
- `terraform.tfvars` - Contains actual values for Linode token, password, etc.

**Vultr (`backend/deployments/{id}/`):**
- `main.tf` - Contains `vultr_instance` and `vultr_firewall_group` resources
- `variables.tf` - Contains Vultr-specific variables like `vultr_api_key`, `os_id`
- `terraform.tfvars` - Contains actual values for Vultr API key, OS ID, etc.

### Common Files Across All Providers

- `startup-script.sh` - Shell script that runs on VM startup (provider-specific)
- `config-template.toml` - Application configuration template with client-specific values
- `outputs.tf` - Terraform outputs for instance IP, ID, etc.
- `.terraform/` - Terraform state directory (created after `terraform init`)

### Cleaning Up Test Files

```bash
# Remove any test deployment directories
rm -rf backend/deployments/test-*
rm -rf backend/deployments/999
```

## Test Coverage

### Providers Tested
- ✅ Google Cloud Platform (GCP)
- ✅ Hetzner Cloud
- ✅ Linode
- ✅ Vultr

### Package Types Tested
- ✅ Pangolin (basic package)
- ✅ Premium (advanced package with additional features)

### Test Scenarios
- ✅ Deployment directory creation
- ✅ Terraform file generation from templates
- ✅ Terraform configuration validation
- ✅ Terraform init execution
- ✅ Provider-specific resource configuration
- ✅ Instance type mapping
- ✅ Region mapping
- ✅ Client-specific naming
- ✅ Premium package features
- ✅ Environment variable handling

## Environment Variables

The tests use mock environment variables, but for integration tests with actual cloud providers, you would need:

### Google Cloud
- `GCP_PROJECT_ID` - Your GCP project ID
- `GCP_CREDENTIALS_FILE` - Path to service account credentials

### Hetzner
- `HCLOUD_TOKEN` - Hetzner Cloud API token

### Linode
- `LINODE_TOKEN` - Linode API token
- `LINODE_ROOT_PASSWORD` - Root password for instances

### Vultr
- `VULTR_API_KEY` - Vultr API key

### Komodo Provider (for all providers)
- `KOMODO_PROVIDER_ENDPOINT` - Komodo API endpoint
- `KOMODO_API_KEY` - Komodo API key
- `KOMODO_API_SECRET` - Komodo API secret
- `GITHUB_TOKEN` - GitHub API token

## Test Data

### Sample Deployment Data
```python
{
    "user_id": 1,
    "package": "Pangolin",
    "cloud_provider": "Google Cloud",
    "region": "us-central1",
    "instance_type": "small",
    "client_name": "test-client",
    "domain": "test.com",
    "admin_email": "<EMAIL>",
    # ... additional fields
}
```

### Premium Package Additional Fields
```python
{
    "package": "Premium",
    "crowdsec_enrollment_key": "test-crowdsec-key",
    "static_page_domain": "static.test.com",
    "oauth_client_id": "test-oauth-id",
    "oauth_client_secret": "test-oauth-secret"
}
```

## Expected Test Results

### Success Criteria
1. **File Generation**: All expected terraform files are created
2. **Content Validation**: Generated files contain provider-specific resources
3. **Terraform Init**: `terraform init` executes without errors
4. **Variable Mapping**: Instance types and regions are correctly mapped
5. **Client Naming**: Client-specific naming is applied consistently

### Common Issues and Solutions

1. **Terraform not found**: Install terraform or add to PATH
2. **Template errors**: Check Jinja2 template syntax
3. **Environment variable issues**: Ensure all required env vars are set
4. **Provider authentication**: For real tests, provide valid credentials

## Adding New Tests

### For New Providers
1. Add provider configuration to `conftest.py`
2. Create provider-specific test in `test_provider_specific.py`
3. Add integration test in `test_terraform_integration.py`
4. Update this README with new provider details

### For New Features
1. Add unit tests to `test_deployment_manager.py`
2. Add integration tests if terraform changes are involved
3. Update test data fixtures in `conftest.py`

## Continuous Integration

These tests are designed to be run in CI/CD pipelines:
- Unit tests run quickly and don't require external dependencies
- Integration tests can be run with terraform installed
- Provider-specific tests validate template generation
- All tests use mocked credentials for security

## Debugging

### Enable verbose output:
```bash
python -m pytest tests/ -v -s
```

### Run specific test:
```bash
python -m pytest tests/test_deployment_manager.py::TestDeploymentManager::test_create_deployment_directory -v
```

### Print generated files (for debugging):
```bash
# Tests will print directory contents and file contents when they fail
python -m pytest tests/test_terraform_integration.py::TestTerraformIntegration::test_gcp_terraform_generation_and_init -v -s
```