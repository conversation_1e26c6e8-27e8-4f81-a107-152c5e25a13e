"""
Pytest configuration and shared fixtures for the test suite.
"""

import pytest
import os
import tempfile
from pathlib import Path
from unittest.mock import patch

# Add the backend directory to the Python path
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


@pytest.fixture
def temp_deployment_dir():
    """Create a temporary directory for deployment testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def mock_environment_variables():
    """Mock environment variables for testing."""
    env_vars = {
        'GCP_PROJECT_ID': 'test-project-123',
        'GCP_CREDENTIALS_FILE': '/tmp/test-creds.json',
        'HCLOUD_TOKEN': 'test-hetzner-token',
        'LINODE_TOKEN': 'test-linode-token',
        'LINODE_ROOT_PASSWORD': 'test-root-password',
        'VULTR_API_KEY': 'test-vultr-key',
        'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
        'KOMODO_API_KEY': 'test-komodo-key',
        'KOMODO_API_SECRET': 'test-komodo-secret',
        'GITHUB_TOKEN': 'test-github-token'
    }
    
    with patch.dict(os.environ, env_vars):
        yield env_vars


@pytest.fixture
def sample_deployment_data():
    """Sample deployment data for testing."""
    return {
        "user_id": 1,
        "package": "Pangolin",
        "cloud_provider": "Google Cloud",
        "region": "us-central1",
        "instance_type": "small",
        "support_level": "basic",
        "cost": 50,
        "client_name": "test-client",
        "domain": "test.com",
        "admin_email": "<EMAIL>",
        "admin_username": "admin",
        "admin_password": "password123",
        "admin_subdomain": "admin",
        "postgres_user": "postgres",
        "postgres_password": "dbpassword",
        "postgres_host": "localhost",
        "github_repo": "test/repo",
    }


@pytest.fixture
def premium_deployment_data():
    """Premium deployment data for testing."""
    return {
        "user_id": 1,
        "package": "Premium",
        "cloud_provider": "Google Cloud",
        "region": "us-central1",
        "instance_type": "medium",
        "support_level": "premium",
        "cost": 100,
        "client_name": "test-premium-client",
        "domain": "premium.test.com",
        "admin_email": "<EMAIL>",
        "admin_username": "admin",
        "admin_password": "password123",
        "admin_subdomain": "admin",
        "postgres_user": "postgres",
        "postgres_password": "dbpassword",
        "postgres_host": "localhost",
        "github_repo": "test/premium-repo",
        "crowdsec_enrollment_key": "test-crowdsec-key",
        "static_page_domain": "static.premium.test.com",
        "oauth_client_id": "test-oauth-id",
        "oauth_client_secret": "test-oauth-secret",
    }


@pytest.fixture
def provider_configs():
    """Configuration for different cloud providers."""
    return {
        "Google Cloud": {
            "region": "us-central1",
            "instance_type": "small",
            "env_vars": {
                'GCP_PROJECT_ID': 'test-project-123',
                'GCP_CREDENTIALS_FILE': '/tmp/test-creds.json',
            }
        },
        "Hetzner": {
            "region": "nbg1",
            "instance_type": "small",
            "env_vars": {
                'HCLOUD_TOKEN': 'test-hetzner-token',
            }
        },
        "Linode": {
            "region": "us-east",
            "instance_type": "small",
            "env_vars": {
                'LINODE_TOKEN': 'test-linode-token',
                'LINODE_ROOT_PASSWORD': 'test-root-password',
            }
        },
        "Vultr": {
            "region": "ewr",
            "instance_type": "small",
            "env_vars": {
                'VULTR_API_KEY': 'test-vultr-key',
            }
        }
    }


def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "terraform: mark test as terraform-related test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names."""
    for item in items:
        # Add terraform marker to terraform tests
        if "terraform" in item.name:
            item.add_marker(pytest.mark.terraform)
        
        # Add slow marker to tests that run terraform
        if "terraform_init" in item.name or "terraform_plan" in item.name:
            item.add_marker(pytest.mark.slow)