"""
Focused tests for terraform file generation and validation.
Tests that all required files are generated correctly and pass terraform validation.
"""

import os
import tempfile
import subprocess
import pytest
from unittest.mock import patch

# Add the backend directory to the Python path
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


from app.core.deployment.deployment_manager import DeploymentManager
from app.core.config import reload_settings
from app.core.deployment.providers.gcp import GoogleCloudStrategy
from app.core.deployment.providers.hetzner import HetznerStrategy
from app.core.deployment.providers.linode import LinodeStrategy
from app.core.deployment.providers.vultr import VultrStrategy
from app.core.deployment.terraform_runner import TerraformRunner

class MockDeployment:
    """Mock deployment object with all required attributes."""
    def __init__(self, cloud_provider, package="Pangolin"):
        self.id = 999
        self.user_id = 1
        self.package = package
        self.cloud_provider = cloud_provider
        self.region = self._get_default_region(cloud_provider)
        self.instance_type = "vc2-1c-1gb"
        self.support_level = "basic"
        self.cost = 50
        self.client_id = "client_test-999"
        self.client_name = "test-client-999"
        self.domain = "test.com"
        self.admin_email = "<EMAIL>"
        self.admin_username = "admin"
        self.admin_password = "password123"
        self.admin_subdomain = "admin"
        self.postgres_user = "postgres"
        self.postgres_password = "dbpassword"
        self.postgres_host = "localhost"
        self.github_repo = "test/repo"
        self.komodo_provider_endpoint = "http://komodo.test:9120"
        self.komodo_api_key = "test-key"
        self.komodo_api_secret = "test-secret"
        self.github_token = "test-token"
        self.komodo_host_ip = "*************"
        
        # Premium package attributes
        if package == "Premium":
            self.crowdsec_enrollment_key = "test-crowdsec-key"
            self.static_page_domain = "static.test.com"
            self.oauth_client_id = "test-oauth-id"
            self.oauth_client_secret = "test-oauth-secret"
            self.openai_api_key = "test-openai-key"
        else:
            self.crowdsec_enrollment_key = None
            self.static_page_domain = None
            self.oauth_client_id = None
            self.oauth_client_secret = None
            self.openai_api_key = None
    
    def _get_default_region(self, cloud_provider):
        """Get default region for each provider."""
        regions = {
            "Google Cloud": "us-central1",
            "Hetzner": "nbg1",
            "Linode": "us-east",
            "Vultr": "ewr"
        }
        return regions.get(cloud_provider, "us-central1")


class TestTerraformGeneration:
    """Test terraform file generation and validation."""

    @pytest.fixture
    def deployment_manager(self):
        """Create a DeploymentManager instance."""
        return DeploymentManager()

    def get_strategy(self, cloud_provider):
        if cloud_provider == "Google Cloud":
            return GoogleCloudStrategy()
        elif cloud_provider == "Hetzner":
            return HetznerStrategy()
        elif cloud_provider == "Linode":
            return LinodeStrategy()
        elif cloud_provider == "Vultr":
            return VultrStrategy()
        else:
            raise ValueError(f"Unsupported cloud provider: {cloud_provider}")

    def setup_environment_variables(self, cloud_provider):
        """Set up environment variables for the given cloud provider."""
        base_vars = {
            'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
            'KOMODO_API_KEY': 'test-komodo-key',
            'KOMODO_API_SECRET': 'test-komodo-secret',
            'GITHUB_TOKEN': 'test-github-token',
            'SSH_PUBLIC_KEY': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDFRYqVx/YHIkpP6qVvMijEBAEjkO3mwQH+JFnQnT9Ks4wOgtm+eTZmtPuXF76JsWyS8h3zYRL8oMKrRxQZbjjTbvCl+/3+5BkNzGOJYJfT3iKzSZCiqGGGYQqpO4MO/vUlbMSCjGxV9Y1yU4nLMJrHIHDXYuGjkMIJ9JLLt8JQC8C9zKdKWCkXRg7SYbt8IiYnp8JAdxbgQZgbU9VCK6oHvLJFZTSbwJQPKYoQJ9vIGYHYJDIzx8TUqvxD8ZjkOUuFYQT+Yb5ABAXO5qVzrJKGsLxhh5BbkZ8OP5QWxCRO5ozfu0eMCo8QvFYKVzQGnNSHBeLxh9xjrKyLR8Utz5J7c8kDSYeOUPiHWYdkpMLqpXqDmqQYZXvLKv+I5QXHmBHvID6693UVnAw7Al+uMhU1ICKGFVWOLMOjEF4DMlNyRsPQAQYHZB5WNlJlOUJnVA0cQqX+XFLKo0S3zU3psUPrZQzL8Iuy7CtW3RdL4YYsrJXcuMXjxwRLcQ0xZdxrNs8= ivob@ivob-xps',
            'SSH_PRIVATE_KEY_PATH': '/tmp/id_rsa_unencrypted',
            'LINODE_TOKEN': 'test-linode-token',
            'VULTR_API_KEY': 'test-vultr-key',
        }
        
        provider_vars = {
            "Google Cloud": {
                'GCP_PROJECT_ID': 'test-project-123',
                'GCP_CREDENTIALS_FILE': '/tmp/test-creds.json',
            },
            "Hetzner": {
                'HCLOUD_TOKEN': 'test-hetzner-token',
            },
            "Linode": {
                'LINODE_TOKEN': 'test-linode-token',
                'LINODE_ROOT_PASSWORD': 'test-root-password',
            },
            "Vultr": {
                'VULTR_API_KEY': 'test-vultr-key',
            }
        }
        
        base_vars.update(provider_vars.get(cloud_provider, {}))
        reload_settings()
        return base_vars



    def run_terraform_command(self, deployment_dir, command):
        """Run a terraform command and return the result."""
        try:
            result = subprocess.run(
                ["terraform", command],
                cwd=deployment_dir,
                capture_output=True,
                text=True,
                timeout=120
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return 1, "", "Terraform command timed out"
        except FileNotFoundError:
            return 1, "", "Terraform not found in PATH"

    def validate_required_files(self, deployment_dir):
        """Validate that all required files are present and non-empty."""
        required_files = [
            'main.tf',
            'variables.tf', 
            'outputs.tf',
            'startup-script.sh',
            'config-template.toml',
            'terraform.tfvars'
        ]
        
        for file_name in required_files:
            file_path = deployment_dir / file_name
            assert file_path.exists(), f"Required file {file_name} was not created"
            assert file_path.stat().st_size > 0, f"Required file {file_name} is empty"

    def validate_file_content(self, deployment_dir, cloud_provider, deployment):
        """Validate that files contain expected content for the provider."""
        # Check main.tf contains provider-specific resources
        main_tf = (deployment_dir / "main.tf").read_text()
        
        if cloud_provider == "Google Cloud":
            assert "google_compute_instance" in main_tf
            assert "google_compute_firewall" in main_tf
            assert "hashicorp/google" in main_tf
        elif cloud_provider == "Hetzner":
            assert "hcloud_server" in main_tf
            assert "hcloud_firewall" in main_tf
            assert "hetznercloud/hcloud" in main_tf
        elif cloud_provider == "Linode":
            assert "linode_instance" in main_tf
            assert "linode_firewall" in main_tf
            assert "linode/linode" in main_tf
        elif cloud_provider == "Vultr":
            assert "vultr_instance" in main_tf
            assert "vultr_firewall" in main_tf
            assert "vultr/vultr" in main_tf
        
        # Check terraform.tfvars contains deployment-specific values
        tfvars = (deployment_dir / "terraform.tfvars").read_text()
        assert deployment.client_name in tfvars
        assert deployment.domain in tfvars
        assert deployment.admin_email in tfvars
        
        # Check Premium package variables if applicable
        if deployment.package == "Premium":
            assert "crowdsec_enrollment_key" in tfvars
            assert "static_page_domain" in tfvars
            assert "oauth_client_id" in tfvars

    @pytest.mark.parametrize("cloud_provider", ["Google Cloud", "Hetzner", "Linode", "Vultr"])
    def test_terraform_file_generation(self, deployment_manager, cloud_provider):
        """Test that terraform files are generated correctly for each provider."""
        deployment = MockDeployment(cloud_provider)
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"\n=== Files being generated in: {temp_dir} ===")
            # Change to temporary directory
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                # Create deployment directory
                deployment_dir = deployment_manager._create_deployment_directory(deployment)
                print(f"Deployment directory: {deployment_dir}")
                
                # Set up environment variables
                env_vars = self.setup_environment_variables(cloud_provider)
                
                with patch.dict(os.environ, env_vars):
                    # Generate terraform files
                    strategy = self.get_strategy(cloud_provider)
                    strategy.generate_terraform_files(deployment_dir, deployment)                      
                
                # Print generated files
                print(f"Generated files in {deployment_dir}:")
                for file in deployment_dir.iterdir():
                    print(f"  - {file.name}")
                
                # Validate all required files are present
                self.validate_required_files(deployment_dir)
                
                # Validate file content
                self.validate_file_content(deployment_dir, cloud_provider, deployment)
                                
            finally:
                os.chdir(original_cwd)

    @pytest.mark.parametrize("cloud_provider", ["Google Cloud", "Hetzner", "Linode", "Vultr"])
    def test_terraform_init_validation(self, deployment_manager, cloud_provider):
        """Test that generated terraform files pass terraform init."""
        deployment = MockDeployment(cloud_provider)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Change to temporary directory
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                # Create deployment directory
                deployment_dir = deployment_manager._create_deployment_directory(deployment)
                
                # Set up environment variables
                env_vars = self.setup_environment_variables(cloud_provider)
                
                with patch.dict(os.environ, env_vars):
                    # Generate terraform files
                    strategy = self.get_strategy(cloud_provider)
                    strategy.generate_terraform_files(deployment_dir, deployment)  


                
                # Run terraform init
                returncode, stdout, stderr = self.run_terraform_command(deployment_dir, "init")
                
                # Assert terraform init succeeded
                assert returncode == 0, f"Terraform init failed for {cloud_provider}:\nSTDOUT: {stdout}\nSTDERR: {stderr}"
                assert "Terraform has been successfully initialized" in stdout, f"Terraform init output unexpected for {cloud_provider}"
                
                # Verify .terraform directory was created
                assert (deployment_dir / ".terraform").exists(), f".terraform directory not created for {cloud_provider}"
                
            finally:
                os.chdir(original_cwd)

    @pytest.mark.parametrize("cloud_provider", ["Google Cloud", "Hetzner", "Linode", "Vultr"])
    def test_terraform_plan_validation(self, deployment_manager, cloud_provider):
        """Test that generated terraform files pass terraform plan."""
        deployment = MockDeployment(cloud_provider)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Change to temporary directory
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                # Create deployment directory
                deployment_dir = deployment_manager._create_deployment_directory(deployment)
                
                # Set up environment variables
                env_vars = self.setup_environment_variables(cloud_provider)
                
                with patch.dict(os.environ, env_vars):
                    # Generate terraform files
                    strategy = self.get_strategy(cloud_provider)
                    strategy.generate_terraform_files(deployment_dir, deployment)  


                
                # Run terraform init
                init_returncode, init_stdout, init_stderr = self.run_terraform_command(deployment_dir, "init")
                assert init_returncode == 0, f"Terraform init failed for {cloud_provider}: {init_stderr}"
                
                # Run terraform plan
                plan_returncode, plan_stdout, plan_stderr = self.run_terraform_command(deployment_dir, "plan")
                
                # Assert terraform plan succeeded (should show resources to be created)
                assert plan_returncode == 0, f"Terraform plan failed for {cloud_provider}:\nSTDOUT: {plan_stdout}\nSTDERR: {plan_stderr}"
                assert "Plan:" in plan_stdout, f"Terraform plan output unexpected for {cloud_provider}"
                
            finally:
                os.chdir(original_cwd)

    @pytest.mark.parametrize("cloud_provider", ["Google Cloud", "Hetzner", "Linode", "Vultr"])
    def test_premium_package_generation(self, deployment_manager, cloud_provider):
        """Test that Premium package files are generated correctly."""
        deployment = MockDeployment(cloud_provider, package="Premium")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Change to temporary directory
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                # Create deployment directory
                deployment_dir = deployment_manager._create_deployment_directory(deployment)
                
                # Set up environment variables
                env_vars = self.setup_environment_variables(cloud_provider)
                
                with patch.dict(os.environ, env_vars):
                    # Generate terraform files
                    strategy = self.get_strategy(cloud_provider)
                    strategy.generate_terraform_files(deployment_dir, deployment)                    
                
                # Validate all required files are present
                self.validate_required_files(deployment_dir)
                
                # Validate Premium-specific content
                tfvars = (deployment_dir / "terraform.tfvars").read_text()
                assert "crowdsec_enrollment_key = \"test-crowdsec-key\"" in tfvars
                assert "static_page_domain = \"static.test.com\"" in tfvars
                assert "oauth_client_id = \"test-oauth-id\"" in tfvars
                assert "oauth_client_secret = \"test-oauth-secret\"" in tfvars
                
            finally:
                os.chdir(original_cwd)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
