import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from app.core.deployment.deployment_manager import DeploymentManager


class TestFailedDeploymentCleanup:
    """Test cleanup functionality for failed deployments."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session and deployment."""
        mock_session = Mock()
        mock_deployment = Mock()
        mock_deployment.id = 1
        mock_deployment.status = "FAILED"
        mock_deployment.user_id = 1
        return mock_session, mock_deployment

    @patch('app.core.deployment.deployment_manager.SessionLocal')
    @patch('app.core.deployment.deployment_manager.TerraformRunner')
    def test_cleanup_failed_deployment_success(self, mock_terraform_runner, mock_session_local, mock_db_session):
        """Test successful cleanup of a failed deployment."""
        mock_session, mock_deployment = mock_db_session
        mock_session_local.return_value = mock_session
        mock_session.query.return_value.filter.return_value.first.return_value = mock_deployment

        # Create a temporary directory to simulate deployment directory
        with tempfile.TemporaryDirectory() as temp_dir:
            deployment_dir = Path(temp_dir) / "deployments" / "1"
            deployment_dir.mkdir(parents=True, exist_ok=True)
            
            # Create some dummy files to simulate terraform files
            (deployment_dir / "main.tf").write_text("# terraform config")
            (deployment_dir / "terraform.tfvars").write_text("# terraform vars")
            
            # Mock the deployment directory path
            with patch('app.core.deployment.deployment_manager.Path') as mock_path:
                mock_path.return_value = deployment_dir
                
                # Setup deployment manager
                manager = DeploymentManager()
                
                # Execute cleanup
                manager.cleanup_failed_deployment(1)
                
                # Verify terraform destroy was called
                mock_terraform_runner.return_value.destroy.assert_called_once()
                
                # Verify deployment status was updated to DESTROYED
                assert mock_deployment.status == "DESTROYED"
                assert mock_session.add.called
                assert mock_session.commit.called

    @patch('app.core.deployment.deployment_manager.SessionLocal')
    @patch('app.core.deployment.deployment_manager.TerraformRunner')
    def test_cleanup_failed_deployment_terraform_destroy_fails(self, mock_terraform_runner, mock_session_local, mock_db_session):
        """Test cleanup when terraform destroy fails (should continue with directory cleanup)."""
        mock_session, mock_deployment = mock_db_session
        mock_session_local.return_value = mock_session
        mock_session.query.return_value.filter.return_value.first.return_value = mock_deployment

        # Make terraform destroy fail
        mock_terraform_runner.return_value.destroy.side_effect = Exception("Terraform destroy failed")

        # Create a temporary directory to simulate deployment directory
        with tempfile.TemporaryDirectory() as temp_dir:
            deployment_dir = Path(temp_dir) / "deployments" / "1"
            deployment_dir.mkdir(parents=True, exist_ok=True)
            
            # Create some dummy files
            (deployment_dir / "main.tf").write_text("# terraform config")
            
            # Mock the deployment directory path and shutil.rmtree
            with patch('app.core.deployment.deployment_manager.Path') as mock_path, \
                 patch('app.core.deployment.deployment_manager.shutil.rmtree') as mock_rmtree:
                mock_deployment_path = Mock()
                mock_deployment_path.exists.return_value = True
                mock_path.return_value = mock_deployment_path
                
                # Setup deployment manager
                manager = DeploymentManager()
                
                # Execute cleanup - should not raise exception even if terraform fails
                manager.cleanup_failed_deployment(1)
                
                # Verify terraform destroy was attempted
                mock_terraform_runner.return_value.destroy.assert_called_once()
                
                # Verify directory removal was attempted
                mock_rmtree.assert_called_once_with(mock_deployment_path)
                
                # Verify deployment status was still updated to DESTROYED
                assert mock_deployment.status == "DESTROYED"
                assert mock_session.add.called
                assert mock_session.commit.called

    @patch('app.core.deployment.deployment_manager.SessionLocal')
    def test_cleanup_failed_deployment_not_found(self, mock_session_local):
        """Test cleanup when deployment is not found - should handle gracefully."""
        mock_session = Mock()
        mock_session_local.return_value = mock_session
        mock_session.query.return_value.filter.return_value.first.return_value = None

        # Setup deployment manager
        manager = DeploymentManager()

        # Mock the deployment directory path to not exist
        with patch('app.core.deployment.deployment_manager.Path') as mock_path:
            mock_path.return_value.exists.return_value = False

            # Execute cleanup - should not raise exception, just handle gracefully
            manager.cleanup_failed_deployment(1)

            # Verify that Path was called to check for leftover directory
            mock_path.assert_called_once_with("backend/deployments/1")

    @patch('app.core.deployment.deployment_manager.SessionLocal')
    def test_cleanup_failed_deployment_not_found_with_leftover_files(self, mock_session_local):
        """Test cleanup when deployment is not found but leftover files exist."""
        mock_session = Mock()
        mock_session_local.return_value = mock_session
        mock_session.query.return_value.filter.return_value.first.return_value = None

        # Setup deployment manager
        manager = DeploymentManager()

        # Mock the deployment directory path to exist (leftover files)
        with patch('app.core.deployment.deployment_manager.Path') as mock_path, \
             patch('app.core.deployment.deployment_manager.TerraformRunner') as mock_terraform_runner, \
             patch('app.core.deployment.deployment_manager.shutil.rmtree') as mock_rmtree:

            mock_deployment_path = Mock()
            mock_deployment_path.exists.return_value = True
            mock_path.return_value = mock_deployment_path

            # Execute cleanup - should clean up leftover files
            manager.cleanup_failed_deployment(1)

            # Verify terraform destroy was attempted
            mock_terraform_runner.assert_called_once_with(mock_deployment_path)
            mock_terraform_runner.return_value.destroy.assert_called_once()

            # Verify directory removal was attempted
            mock_rmtree.assert_called_once_with(mock_deployment_path)

    @patch('app.core.deployment.deployment_manager.SessionLocal')
    def test_cleanup_failed_deployment_no_directory(self, mock_session_local, mock_db_session):
        """Test cleanup when deployment directory doesn't exist."""
        mock_session, mock_deployment = mock_db_session
        mock_session_local.return_value = mock_session
        mock_session.query.return_value.filter.return_value.first.return_value = mock_deployment

        # Mock the deployment directory path to not exist
        with patch('app.core.deployment.deployment_manager.Path') as mock_path:
            mock_path.return_value.exists.return_value = False
            
            # Setup deployment manager
            manager = DeploymentManager()
            
            # Execute cleanup
            manager.cleanup_failed_deployment(1)
            
            # Verify deployment status was updated to DESTROYED even without directory
            assert mock_deployment.status == "DESTROYED"
            assert mock_session.add.called
            assert mock_session.commit.called
