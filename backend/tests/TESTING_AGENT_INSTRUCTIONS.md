# Deployment Manager Tests

This file contains instructions for testing agents to regression test for the deployment manager functionality, specifically testing terraform configuration generation and validation for all cloud providers.


The following are your instructions 
1) Currently the code base is producing valid terraform config files. We need to keep this. We need to ensure that every changes does not break the code that is working. This is regression testing

2) When you complete your coding changes you need to ensure that the following regression tests pass.

3) You will need to do multiple steps, some involving creating files using scripts and some involed in running commands and verifying the commands success. We are not using a testing library so please dont suggest one

4) When you finish a change and want to verify regression testing by Creating deployments for each provider (run from root folder):

**Google Cloud:**
```bash
cd backend

python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.gcp import GoogleCloudStrategy

deployment = MockDeployment('Google Cloud')
deployment.id = 'test-gcp'

os.environ.update({
    'GCP_PROJECT_ID': 'test-project-123',
    'GCP_CREDENTIALS_FILE': '/tmp/test-creds.json',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Google Cloud strategy and call generate_terraform_files on it
gcp_strategy = GoogleCloudStrategy()
gcp_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Google Cloud files created in: {deployment_dir}')
"
```

**Hetzner:**
```bash
python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.hetzner import HetznerStrategy

deployment = MockDeployment('Hetzner')
deployment.id = 'test-hetzner'

os.environ.update({
    'HCLOUD_TOKEN': 'test-hetzner-token--12345678901234567890123456789012345678901234',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Hetzner strategy and call generate_terraform_files on it
hetzner_strategy = HetznerStrategy()
hetzner_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Hetzner files created in: {deployment_dir}')
"
```

**Linode:**
```bash
python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.linode import LinodeStrategy

deployment = MockDeployment('Linode')
deployment.id = 'test-linode'

os.environ.update({
    'LINODE_TOKEN': 'test-linode-token',
    'LINODE_ROOT_PASSWORD': 'test-root-password',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Linode strategy and call generate_terraform_files on it
linode_strategy = LinodeStrategy()
linode_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Linode files created in: {deployment_dir}')
"
```

**Vultr:**
```bash
python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.vultr import VultrStrategy

deployment = MockDeployment('Vultr')
deployment.id = 'test-vultr'

os.environ.update({
    'VULTR_API_KEY': 'test-vultr-key',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Vultr strategy and call generate_terraform_files on it
vultr_strategy = VultrStrategy()
vultr_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Vultr files created in: {deployment_dir}')
"
```

**Digital Ocean:**
```bash
python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.digitalocean import DigitalOceanStrategy

deployment = MockDeployment('Digital Ocean')
deployment.id = 'test-digitalocean'

os.environ.update({
    'DIGITALOCEAN_TOKEN': 'test-linode-token',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Digital Ocean strategy and call generate_terraform_files on it
digitalocean_strategy = DigitalOceanStrategy()
digitalocean_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Digital Ocean files created in: {deployment_dir}')
"
```

**Digital Ocean:**
```bash
python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.awslightsail import AwsLightsailStrategy

deployment = MockDeployment('AWS Lightsail')
deployment.id = 'test-awslightsail'

os.environ.update({
    'DIGITALOCEAN_TOKEN': 'test-linode-token',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the AWS Lightsail strategy and call generate_terraform_files on it
awslightsail_strategy = AwsLightsailStrategy()
awslightsail_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'AWS Lightsail files created in: {deployment_dir}')
"
```

**Premium Package Example (any provider):**
```bash
python -c "
import os, sys
sys.path.insert(0, os.getcwd())
from tests.test_terraform_generation import MockDeployment
from app.core.deployment.deployment_manager import DeploymentManager
from app.core.deployment.providers.gcp import GoogleCloudStrategy

deployment = MockDeployment('Google Cloud', package='Premium')
deployment.id = 'test-gcp-premium'

os.environ.update({
    'GCP_PROJECT_ID': 'test-project-123',
    'GCP_CREDENTIALS_FILE': '/tmp/test-creds.json',
    'KOMODO_PROVIDER_ENDPOINT': 'http://komodo.test:9120',
    'KOMODO_API_KEY': 'test-key',
    'KOMODO_API_SECRET': 'test-secret',
    'GITHUB_TOKEN': 'test-token',
    'CROWDSEC_ENROLLMENT_KEY': 'test-crowdsec-key',
    'STATIC_PAGE_DOMAIN': 'test-static-page-domain',
    'OAUTH_CLIENT_ID': 'test-oauth-client-id',
    'OAUTH_CLIENT_SECRET': 'test-oauth-client-secret',
    'KOMODO_HOST_IP': '*******',
    'OPENAI_API_KEY':'test-openai-api-key'
})

dm = DeploymentManager()
deployment_dir = dm._create_deployment_directory(deployment)

# Get the Google Cloud strategy and call generate_terraform_files on it
gcp_strategy = GoogleCloudStrategy()
gcp_strategy.generate_terraform_files(deployment_dir, deployment)
print(f'Premium Google Cloud files created in: {deployment_dir}')
"
```

## Generated Files Location
The scripts above create temporary directories for each test run.

```
backend/deployments/{deployment_id}/
├── main.tf              # Provider-specific terraform configuration
├── variables.tf         # Variable definitions
├── outputs.tf          # Output definitions
├── terraform.tfvars    # Variable values
├── startup-script.sh   # VM initialization script
├── config-template.toml # Application configuration
└── .terraform/         # Terraform state (after terraform init)
```

**Testing the generated files:**
When you have generated the files you need to test them by doing the following

```bash
# Comment out komodo-provider sections for terraform init
./tests/toggle_komodo_provider.sh backend/deployments/{deployment_id} comment

# Run terraform init
cd backend/deployments/{deployment_id}
terraform init #ensure that there are no errors returned from the terraform init command

# Uncomment komodo-provider sections for terraform plan (if needed)
cd ../../..
./tests/toggle_komodo_provider.sh backend/deployments/{deployment_id} uncomment

# Run terraform plan
cd backend/deployments/{deployment_id}
terraform plan #ensure that there are no errors returned from the terraform plan command (warning are ok)

# Clean up all {deployment_id}s
cd ../../..
rm -rf backend/deployments/{deployment_id}
```

ensure that all {deployment_id}s are cleaned up before going back to your next coding change.

## Agent Testing Notes and Refinements

**Important Prerequisites:**
1. **Always activate the virtual environment first**: Before running any tests, make sure to run `source venv/bin/activate` from the backend directory.

**Process Management Tips:**
1. **Handle hanging processes**: Some terraform commands may appear to hang after completion. Use `list-processes` to check running processes and `kill-process` to terminate hanging ones if needed.
2. **Wait for process completion**: Always ensure processes complete before starting new ones to avoid "Cannot launch another waiting process" errors.
3. **Combine commands when possible**: Use `&&` to chain commands (e.g., `./tests/toggle_komodo_provider.sh ... && cd ... && terraform init`) to reduce process management overhead.

**Expected Results:**
- All terraform init commands should complete successfully with no errors
- All terraform plan commands should complete successfully (warnings are acceptable)
- The following warnings are normal and expected:
  - "Provider development overrides are in effect" warnings
  - "Value for undeclared variable" warnings for variables like `package`, `instance_type`, `komodo_host_ip`
  - Deprecated attribute warnings (e.g., for Linode `ip_address` attribute)

**Test Summary:**
When all tests pass successfully, you should see:
- ✅ Google Cloud: terraform init and plan successful
- ✅ Hetzner: terraform init and plan successful
- ✅ Linode: terraform init and plan successful
- ✅ Vultr: terraform init and plan successful
- ✅ Premium Package (Google Cloud): terraform init and plan successful

**Troubleshooting:**
- If terraform commands hang, check for running processes and kill them if necessary
- If you get "Cannot launch another waiting process" errors, wait for current processes to complete or kill hanging ones
- All deployment directories should be cleaned up after each test to avoid conflicts
contextware.ai