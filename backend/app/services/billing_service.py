from decimal import Decimal
from sqlalchemy.orm import Session
from typing import Tu<PERSON>, Dict, Any
from datetime import datetime

from app.models.user import User
from app.models.transaction import Transaction
from app.services.referral_service import ReferralService
from app.services.signup_credit_service import SignupCreditService


class BillingService:
    """
    Comprehensive billing service that handles regular balance and credits.
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.referral_service = ReferralService(db)
        self.signup_service = SignupCreditService(db)
    
    def get_user_total_balance(self, user: User) -> Dict[str, Decimal]:
        """
        Get user's complete balance breakdown including all credit types.
        
        Returns:
            Dict with regular_balance, referral_credits, signup_credits, total_balance
        """
        regular_balance = user.balance or Decimal("0")
        referral_credits = self.referral_service.calculate_total_available_credit(user.id)
        signup_credits = self.signup_service.get_usable_signup_balance(user.id)
        total_balance = regular_balance + referral_credits + signup_credits
        
        return {
            "regular_balance": regular_balance,
            "referral_credits": referral_credits,
            "signup_credits": signup_credits,
            "total_balance": total_balance
        }
    
    def charge_user(self, user: User, amount: Decimal, description: str) -> Dict[str, Any]:
        """
        Charge a user for services, using credits first, then regular balance.
        
        Args:
            user: User to charge
            amount: Amount to charge in EUR
            description: Description for the transaction
            
        Returns:
            Dict with charge breakdown and transaction details
        """
        if amount <= 0:
            return {
                "success": True,
                "amount_charged": Decimal("0"),
                "credits_used": Decimal("0"),
                "regular_balance_used": Decimal("0"),
                "transactions": []
            }
        
        # Get current balances
        balance_info = self.get_user_total_balance(user)
        
        if balance_info["total_balance"] < amount:
            return {
                "success": False,
                "error": f"Insufficient funds. Required: {amount}, Available: {balance_info['total_balance']}",
                "balance_info": balance_info
            }
        
        remaining_to_charge = amount
        credits_used = Decimal("0")
        regular_balance_used = Decimal("0")
        transactions = []
        
        # 1. Use signup credits first (oldest first)
        if remaining_to_charge > 0 and balance_info["signup_credits"] > 0:
            signup_used = self.signup_service.use_signup_credit(user.id, remaining_to_charge)
            if signup_used > 0:
                credits_used += signup_used
                remaining_to_charge -= signup_used
                
                # Create transaction record
                tx = Transaction(
                    user_id=user.id,
                    amount=-signup_used,
                    type="billing_signup_credit",
                    description=f"{description} (Signup Credit)"
                )
                self.db.add(tx)
                transactions.append(tx)
        
        # 2. Use referral credits next (oldest first)
        if remaining_to_charge > 0 and balance_info["referral_credits"] > 0:
            referral_used = self.referral_service.use_referral_credit(user.id, remaining_to_charge)
            if referral_used > 0:
                credits_used += referral_used
                remaining_to_charge -= referral_used
                
                # Create transaction record
                tx = Transaction(
                    user_id=user.id,
                    amount=-referral_used,
                    type="billing_referral_credit",
                    description=f"{description} (Referral Credit)"
                )
                self.db.add(tx)
                transactions.append(tx)
        
        # 3. Use regular balance for any remaining amount
        if remaining_to_charge > 0:
            user.balance = (user.balance or Decimal("0")) - remaining_to_charge
            regular_balance_used = remaining_to_charge
            
            # Create transaction record
            tx = Transaction(
                user_id=user.id,
                amount=-remaining_to_charge,
                type="billing",
                description=description
            )
            self.db.add(tx)
            self.db.add(user)
            transactions.append(tx)
        
        # Commit all changes
        self.db.commit()
        
        return {
            "success": True,
            "amount_charged": amount,
            "credits_used": credits_used,
            "regular_balance_used": regular_balance_used,
            "transactions": transactions,
            "final_balance": self.get_user_total_balance(user)
        }
    
    def expire_credits(self, user_id: int = None) -> Dict[str, int]:
        """
        Expire credits that have passed their expiry date.
        
        Args:
            user_id: If provided, only expire credits for this user. Otherwise expire for all users.
            
        Returns:
            Dict with counts of expired credits
        """
        now = datetime.utcnow()
        
        # Expire referral credits
        from app.models.referral_credit import ReferralCredit
        from app.models.signup_credit import SignupCredit

        referral_query = self.db.query(ReferralCredit).filter(
            ReferralCredit.is_active == True,
            ReferralCredit.expires_at.isnot(None),
            ReferralCredit.expires_at <= now
        )

        if user_id:
            referral_query = referral_query.filter(ReferralCredit.user_id == user_id)

        expired_referral_credits = referral_query.all()
        for credit in expired_referral_credits:
            credit.is_active = False
            self.db.add(credit)

        # Expire signup credits
        signup_query = self.db.query(SignupCredit).filter(
            SignupCredit.is_active == True,
            SignupCredit.expires_at.isnot(None),
            SignupCredit.expires_at <= now
        )

        if user_id:
            signup_query = signup_query.filter(SignupCredit.user_id == user_id)

        expired_signup_credits = signup_query.all()
        for credit in expired_signup_credits:
            credit.is_active = False
            self.db.add(credit)
        
        self.db.commit()
        
        return {
            "expired_referral_credits": len(expired_referral_credits),
            "expired_signup_credits": len(expired_signup_credits)
        }
