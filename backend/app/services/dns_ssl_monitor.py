"""
DNS and SSL Certificate Checking Utilities

Simple utilities to check:
1. DNS resolution - checks if domain/subdomain points to the correct VPS IP
2. SSL certificate status - checks if SSL certificates are properly issued
"""

import logging
import socket
import ssl
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)

class DNS<PERSON>LChecker:
    """Simple DNS resolution and SSL certificate checker"""
        
    def check_dns_resolution(self, domain: str, expected_ip: str) -> Tuple[bool, str]:
        """
        Check if domain resolves to the expected IP address

        Args:
            domain: Domain or subdomain to check
            expected_ip: Expected IP address

        Returns:
            Tuple of (is_resolved_correctly, message)
        """
        try:
            # Validate inputs
            if not domain or not expected_ip:
                return False, "Invalid domain or IP address provided"

            # Use Google's DNS API for reliable DNS resolution
            response = requests.get(
                f"https://dns.google/resolve?name={domain}&type=A",
                timeout=10
            )

            if response.status_code == 200:
                dns_data = response.json()

                # Check for DNS resolution errors
                if dns_data.get('Status') != 0:
                    status_code = dns_data.get('Status', 'unknown')
                    error_messages = {
                        1: "Format error - DNS query was malformed",
                        2: "Server failure - DNS server encountered an error",
                        3: "Name error - Domain name does not exist",
                        4: "Not implemented - DNS server does not support this query type",
                        5: "Refused - DNS server refused to answer"
                    }
                    error_msg = error_messages.get(status_code, f"DNS error with status code {status_code}")
                    return False, f"DNS resolution failed: {error_msg}"

                if 'Answer' in dns_data:
                    resolved_ips = [answer['data'] for answer in dns_data['Answer'] if answer['type'] == 1]

                    if not resolved_ips:
                        return False, f"No A records found for {domain}"

                    if expected_ip in resolved_ips:
                        return True, f"DNS correctly resolves {domain} to {expected_ip}"
                    else:
                        suggestion = "Please update your DNS records to point to the correct IP address"
                        return False, f"DNS resolves {domain} to {resolved_ips}, expected {expected_ip}. {suggestion}"
                else:
                    return False, f"No DNS A record found for {domain}. Please add an A record pointing to {expected_ip}"
            else:
                return False, f"DNS query failed with HTTP status {response.status_code}. Please try again later"

        except requests.exceptions.Timeout:
            return False, f"DNS query timed out for {domain}. Please check your internet connection"
        except requests.exceptions.ConnectionError:
            return False, f"Failed to connect to DNS service. Please check your internet connection"
        except requests.exceptions.RequestException as e:
            logger.error(f"DNS resolution request failed for {domain}: {e}")
            return False, f"DNS query failed: Network error"
        except Exception as e:
            logger.error(f"Unexpected error during DNS resolution check for {domain}: {e}")
            return False, f"DNS check failed: Unexpected error occurred"
    
    def check_ssl_certificate(self, domain: str, port: int = 443) -> Tuple[bool, str]:
        """
        Check SSL certificate status for a domain

        Args:
            domain: Domain to check SSL certificate for
            port: Port to check (default 443)

        Returns:
            Tuple of (is_ssl_valid, message)
        """
        try:
            # Validate inputs
            if not domain:
                return False, "Invalid domain provided for SSL check"

            # Create SSL context
            context = ssl.create_default_context()

            # Connect and get certificate
            with socket.create_connection((domain, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    cert = ssock.getpeercert()

                    if not cert:
                        return False, "No SSL certificate found"

                    # Check if certificate is valid
                    try:
                        not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                        not_before = datetime.strptime(cert['notBefore'], '%b %d %H:%M:%S %Y %Z')
                        now = datetime.now()

                        if now < not_before:
                            return False, f"SSL certificate not yet valid (valid from {not_before.strftime('%Y-%m-%d')})"
                        elif now > not_after:
                            return False, f"SSL certificate expired on {not_after.strftime('%Y-%m-%d')}"
                        else:
                            days_until_expiry = (not_after - now).days
                            if days_until_expiry <= 7:
                                return False, f"SSL certificate expires soon ({days_until_expiry} days). Consider renewal"
                            elif days_until_expiry <= 30:
                                return True, f"SSL certificate valid, expires in {days_until_expiry} days (renewal recommended)"
                            else:
                                return True, f"SSL certificate valid, expires in {days_until_expiry} days"

                    except ValueError as e:
                        return False, f"Invalid certificate date format: {str(e)}"

        except ssl.SSLCertVerificationError as e:
            error_msg = str(e)
            if "certificate verify failed" in error_msg.lower():
                return False, "SSL certificate verification failed. Certificate may be self-signed or invalid"
            else:
                return False, f"SSL certificate verification error: {error_msg}"
        except ssl.SSLError as e:
            error_msg = str(e)
            if "certificate unknown" in error_msg.lower():
                return False, "SSL certificate not recognized by the server"
            elif "handshake failure" in error_msg.lower():
                return False, "SSL handshake failed. Server may not support SSL/TLS"
            else:
                return False, f"SSL error: {error_msg}"
        except socket.timeout:
            return False, f"Connection timeout while checking SSL certificate for {domain}. Server may be down"
        except socket.gaierror as e:
            return False, f"DNS resolution failed for {domain}: {str(e)}"
        except ConnectionRefusedError:
            return False, f"Connection refused on port {port}. Server may not be running or port may be blocked"
        except Exception as e:
            logger.error(f"Unexpected error during SSL certificate check for {domain}: {e}")
            return False, f"SSL check failed: Unexpected error occurred"
    
    def get_deployment_domains(self, deployment) -> List[str]:
        """
        Get all domains/subdomains for a deployment

        Args:
            deployment: Deployment object with domain attributes

        Returns:
            List of domains to check
        """
        domains = []

        if hasattr(deployment, 'domain') and deployment.domain:
            # Only check subdomains that users actually configure
            # Most users set up wildcard DNS (*.domain.com) for subdomains, not the main domain

            if hasattr(deployment, 'admin_subdomain') and deployment.admin_subdomain:
                # Main admin subdomain (this is the primary access point)
                admin_domain = f"{deployment.admin_subdomain}.{deployment.domain}"
                domains.append(admin_domain)

            if hasattr(deployment, 'static_page_subdomain') and deployment.static_page_subdomain:
                # Static page subdomain (for Pangolin+ packages)
                static_domain = f"{deployment.static_page_subdomain}.{deployment.domain}"
                domains.append(static_domain)

            # Don't check the main domain - users typically only set up wildcard DNS for subdomains

        return domains
    
    def check_deployment_dns_ssl(self, deployment) -> Dict[str, any]:
        """
        Check DNS and SSL status for a deployment

        Args:
            deployment: Deployment object with required attributes

        Returns:
            Dictionary with check results
        """
        deployment_id = getattr(deployment, 'id', 'unknown')

        # Get the public IP address for this deployment
        # instance_ip: for new cloud instances
        # vps_ip_address: for BYOVPS deployments
        target_ip = (getattr(deployment, 'instance_ip', None) or
                    getattr(deployment, 'vps_ip_address', None))

        if not target_ip:
            return {
                'deployment_id': deployment_id,
                'status': 'no_ip',
                'message': 'No public IP address available for checking (checked instance_ip, vps_ip_address)',
                'dns_checks': [],
                'ssl_checks': [],
                'needs_restart': False
            }
        
        domains = self.get_deployment_domains(deployment)
        dns_checks = []
        ssl_checks = []
        needs_restart = False

        for domain in domains:
            # Check DNS resolution
            dns_ok, dns_message = self.check_dns_resolution(domain, target_ip)
            dns_checks.append({
                'domain': domain,
                'resolved_correctly': dns_ok,
                'message': dns_message
            })

            # Only check SSL if DNS is resolving correctly
            if dns_ok:
                ssl_ok, ssl_message = self.check_ssl_certificate(domain)
                ssl_checks.append({
                    'domain': domain,
                    'ssl_valid': ssl_ok,
                    'message': ssl_message
                })

                # If SSL is failing, we might need a restart
                if not ssl_ok and "SSL error" in ssl_message:
                    needs_restart = True
            else:
                ssl_checks.append({
                    'domain': domain,
                    'ssl_valid': False,
                    'message': 'Skipped SSL check - DNS not resolving correctly'
                })
        
        # Determine overall status with detailed categorization
        all_dns_ok = all(check['resolved_correctly'] for check in dns_checks)
        all_ssl_ok = all(check['ssl_valid'] for check in ssl_checks if check['ssl_valid'] is not False)

        # Count specific issues for better user feedback
        dns_failed_count = len([c for c in dns_checks if not c['resolved_correctly']])
        ssl_failed_count = len([c for c in ssl_checks if not c['ssl_valid']])

        if all_dns_ok and all_ssl_ok:
            status = 'healthy'
            message = f"All {len(domains)} domain(s) are properly configured with valid SSL certificates"
        elif all_dns_ok and not all_ssl_ok:
            status = 'ssl_issues'
            needs_restart = True
            message = f"DNS is configured correctly, but {ssl_failed_count} domain(s) have SSL certificate issues"
        elif not all_dns_ok and all_ssl_ok:
            status = 'dns_issues'
            message = f"{dns_failed_count} domain(s) have DNS configuration issues, but SSL certificates are valid"
        elif not all_dns_ok and not all_ssl_ok:
            status = 'multiple_issues'
            needs_restart = True
            message = f"{dns_failed_count} domain(s) have DNS issues and {ssl_failed_count} domain(s) have SSL issues"
        else:
            status = 'unknown'
            message = "Unable to determine deployment status"
        
        return {
            'deployment_id': deployment_id,
            'status': status,
            'message': message,
            'dns_checks': dns_checks,
            'ssl_checks': ssl_checks,
            'needs_restart': needs_restart,
            'checked_at': datetime.now().isoformat(),
            'summary': {
                'total_domains': len(domains),
                'dns_ok_count': len(domains) - dns_failed_count,
                'ssl_ok_count': len(domains) - ssl_failed_count,
                'dns_failed_count': dns_failed_count,
                'ssl_failed_count': ssl_failed_count
            }
        }

# Global instance for use in API endpoints
dns_ssl_checker = DNSSSLChecker()
