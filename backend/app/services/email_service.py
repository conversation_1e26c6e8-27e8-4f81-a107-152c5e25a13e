from __future__ import annotations

from typing import Any, Sequence
from datetime import datetime
import requests
from jinja2 import Environment, FileSystemLoader, select_autoescape
from pathlib import Path

from app.core.config import get_settings

_templates_env = Environment(
    loader=FileSystemLoader(str(Path(__file__).parent.parent / "templates" / "emails")),
    autoescape=select_autoescape(enabled_extensions=("html", "xml"), default_for_string=False),
    trim_blocks=True,
    lstrip_blocks=True,
)


def _send_email(to: Sequence[str] | str, subject: str, text: str, html: str | None = None) -> bool:
    settings = get_settings()
    if not settings.USE_EMAIL:
        return False

    api_url = settings.MAILGUN_API_URL.rstrip("/") + "/messages"
    from_addr = settings.EMAIL_FROM
    cc_addr = settings.SUPPORT_EMAIL
    auth = ("api", settings.MAILGUN_API_KEY) if settings.MAILGUN_API_KEY else None

    data = {
        "from": from_addr,
        "to": ", ".join(to) if isinstance(to, (list, tuple)) else to,
        "cc": cc_addr,
        "subject": subject,
        "text": text,
    }
    if html:
        data["html"] = html

    try:
        resp = requests.post(api_url, data=data, auth=auth, timeout=10)
        resp.raise_for_status()
        return True
    except Exception as e:
        # Intentionally don't raise to avoid breaking flows
        print(f"[EMAIL] Failed to send email: {e}")
        return False


def send_verification_email(email: str, username: str, token: str) -> bool:
    settings = get_settings()
    if not settings.USE_EMAIL:
        return False
    verify_link = f"{settings.VERIFICATION_BASE_URL}?token={token}"
    text_tmpl = _templates_env.get_template("verification.txt.j2")
    html_tmpl = _templates_env.get_template("verification.html.j2")
    text = text_tmpl.render(username=username, verify_link=verify_link)
    html = html_tmpl.render(username=username, verify_link=verify_link)
    subject = "Verify your Manidae Cloud account"
    return _send_email(email, subject, text, html)


def send_deployment_active_email(email: str, username: str, deployment: Any) -> bool:
    settings = get_settings()
    if not settings.USE_EMAIL:
        return False

    # Calculate user-facing daily cost based on billing interval
    hourly_cost_cents = getattr(deployment, "cost", 0) or 0
    billing_interval_minutes = getattr(settings, 'BILLING_INTERVAL_MINUTES', 1440)  # Default to daily

    # Convert hourly cost in cents to daily cost in EUR
    # Users are charged for full billing periods (typically 24 hours)
    if billing_interval_minutes == 1440:  # Daily billing
        daily_cost_eur = (hourly_cost_cents * 24) / 100  # 24 hours * cents to EUR
        cost_display = f"€{daily_cost_eur:.2f} per day"
    else:
        # For other intervals, show hourly cost in EUR
        hourly_cost_eur = hourly_cost_cents / 100
        cost_display = f"€{hourly_cost_eur:.4f} per hour"

    ctx = {
        "username": username,
        "deployment": {
            "id": getattr(deployment, "id", None),
            "package": getattr(deployment, "package", None),
            "cloud_provider": getattr(deployment, "cloud_provider", None),
            "region": getattr(deployment, "region", None),
            "instance_type": getattr(deployment, "instance_type", None),
            "instance_ip": getattr(deployment, "instance_ip", None),
            "domain": getattr(deployment, "domain", None),
            "admin_subdomain": getattr(deployment, "admin_subdomain", None),
            "created_at": getattr(deployment, "created_at", None),
            "cost_display": cost_display,  # User-friendly cost display
            "daily_cost_eur": f"€{daily_cost_eur:.2f}" if billing_interval_minutes == 1440 else cost_display,
        }
    }
    text = _templates_env.get_template("deployment_active.txt.j2").render(**ctx)
    html = _templates_env.get_template("deployment_active.html.j2").render(**ctx)

    # Create informative subject based on whether DNS setup is required
    deployment_id = getattr(deployment, 'id', '')
    domain = getattr(deployment, 'domain', None)
    if domain:
        subject = f"🎉 VPS #{deployment_id} is LIVE - DNS Setup Required"
    else:
        subject = f"🎉 Deployment #{deployment_id} is LIVE and Ready"

    return _send_email(email, subject, text, html)


def send_low_balance_warning(email: str, username: str, balance: float, min_balance: float) -> bool:
    settings = get_settings()
    if not settings.USE_EMAIL:
        return False
    ctx = {"username": username, "balance": balance, "min_balance": min_balance}
    text = _templates_env.get_template("low_balance_warning.txt.j2").render(**ctx)
    html = _templates_env.get_template("low_balance_warning.html.j2").render(**ctx)
    subject = "Low balance warning"
    return _send_email(email, subject, text, html)


def send_termination_notice(email: str, username: str, terminated_deployments: list[int], balance: float) -> bool:
    settings = get_settings()
    if not settings.USE_EMAIL:
        return False
    ctx = {
        "username": username,
        "terminated_deployments": terminated_deployments,
        "balance": balance,
        "timestamp": datetime.utcnow().isoformat(timespec="seconds"),
    }
    text = _templates_env.get_template("termination_notice.txt.j2").render(**ctx)
    html = _templates_env.get_template("termination_notice.html.j2").render(**ctx)
    subject = "Deployments terminated due to insufficient balance"
    return _send_email(email, subject, text, html)


def send_password_reset_email(email: str, username: str, token: str) -> bool:
    settings = get_settings()
    if not settings.USE_EMAIL:
        return False
    reset_link = f"{getattr(settings, 'PASSWORD_RESET_BASE_URL', settings.VERIFICATION_BASE_URL.replace('/verify-email', '/reset-password'))}?token={token}"
    ctx = {"username": username, "reset_link": reset_link}
    text = _templates_env.get_template("password_reset.txt.j2").render(**ctx)
    html = _templates_env.get_template("password_reset.html.j2").render(**ctx)
    subject = "Reset your Manidae Cloud password"
    return _send_email(email, subject, text, html)


def send_support_request_email(user_name: str, user_email: str, subject: str, message: str) -> bool:
    settings = get_settings()
    if not settings.USE_EMAIL:
        return False

    # Send to support team
    support_email = settings.SUPPORT_EMAIL

    ctx = {
        "user_name": user_name,
        "user_email": user_email,
        "subject": subject,
        "message": message,
        "timestamp": datetime.utcnow().isoformat(timespec="seconds"),
    }
    text = _templates_env.get_template("support_request.txt.j2").render(**ctx)
    html = _templates_env.get_template("support_request.html.j2").render(**ctx)
    email_subject = f"Support Request: {subject}"
    return _send_email(support_email, email_subject, text, html)


def send_referral_request_email(user_email: str, reason: str) -> bool:
    settings = get_settings()
    if not settings.USE_EMAIL:
        return False

    # Send to support team
    support_email = settings.SUPPORT_EMAIL

    ctx = {
        "user_email": user_email,
        "reason": reason,
        "timestamp": datetime.utcnow().isoformat(timespec="seconds"),
    }
    text = _templates_env.get_template("referral_request.txt.j2").render(**ctx)
    html = _templates_env.get_template("referral_request.html.j2").render(**ctx)
    email_subject = f"Referral Credit Request from {user_email}"
    return _send_email(support_email, email_subject, text, html)

