import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.transaction import Transaction
from app.models.deployment import Deployment
from app.models.user import User

def check_status(user_id: int):
    db: Session = SessionLocal()
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            print(f"User {user_id} not found.")
            return

        print(f"-- User Balance --")
        print(f"Current Balance for user #{user.id}: {user.balance:.4f} EUR")

        deployment = db.query(Deployment).filter(Deployment.user_id == user_id, Deployment.status.in_(['ACTIVE', 'READY', 'DNS_FAILED'])).first()
        if deployment:
            print(f"\n-- Deployment Status --")
            print(f"Deployment #{deployment.id} 'last_billed_at': {deployment.last_billed_at}")

        transaction = db.query(Transaction).filter(Transaction.user_id == user_id, Transaction.type == 'billing').order_by(Transaction.created_at.desc()).first()
        if transaction:
            print(f"\n-- Last Billing Transaction --")
            print(f"Description: {transaction.description}")
            print(f"Amount: {transaction.amount:.4f} EUR")

    finally:
        db.close()

if __name__ == "__main__":
    # Get user ID from command line argument, default to 1
    USER_ID_TO_CHECK = int(sys.argv[1]) if len(sys.argv) > 1 else 1
    print(f"Checking status for user ID: {USER_ID_TO_CHECK}...")
    check_status(USER_ID_TO_CHECK)

