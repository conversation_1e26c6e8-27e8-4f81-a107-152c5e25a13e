from sqlalchemy.orm import Session
from sqlalchemy import func
from decimal import Decimal
from datetime import datetime, timedelta
from typing import List, Optional

from app.models.user import User
from app.models.signup_credit import SignupCredit
from app.models.transaction import Transaction
from app.core.config import get_settings


class SignupCreditService:
    def __init__(self, db: Session):
        self.db = db
        self.settings = get_settings()
    
    def create_signup_credit(self, user: User) -> Optional[SignupCredit]:
        """
        Create signup credit for a new user if configured.
        
        Args:
            user: The new user
            
        Returns:
            SignupCredit instance if created, None if amount is 0
        """
        if self.settings.SIGNUP_CREDIT_AMOUNT <= 0:
            return None
            
        now = datetime.utcnow()
        
        # Calculate expiry date if configured
        expires_at = None
        if self.settings.SIGNUP_CREDIT_EXPIRY_DAYS > 0:
            expires_at = now + timedelta(days=self.settings.SIGNUP_CREDIT_EXPIRY_DAYS)
        
        # Create signup credit
        signup_credit = SignupCredit(
            user_id=user.id,
            amount=Decimal(str(self.settings.SIGNUP_CREDIT_AMOUNT)),
            expires_at=expires_at,
            used_amount=Decimal("0"),
            is_active=True
        )
        
        self.db.add(signup_credit)
        
        # Create transaction record
        signup_transaction = Transaction(
            user_id=user.id,
            amount=Decimal(str(self.settings.SIGNUP_CREDIT_AMOUNT)),
            type="signup_credit",
            description="Welcome signup credit"
        )
        
        self.db.add(signup_transaction)
        self.db.commit()
        self.db.refresh(signup_credit)
        
        return signup_credit
    
    def get_user_signup_credits(self, user_id: int) -> List[SignupCredit]:
        """Get all signup credits for a user"""
        return self.db.query(SignupCredit).filter(
            SignupCredit.user_id == user_id
        ).order_by(SignupCredit.created_at.desc()).all()
    
    def get_usable_signup_balance(self, user_id: int) -> Decimal:
        """Get total usable signup credit balance for a user"""
        credits = self.db.query(SignupCredit).filter(
            SignupCredit.user_id == user_id,
            SignupCredit.is_active == True
        ).all()
        
        total = Decimal("0")
        for credit in credits:
            if credit.is_usable:
                total += credit.remaining_amount
        
        return total
    
    def get_total_signup_balance(self, user_id: int) -> Decimal:
        """Get total signup credit balance (including expired/used)"""
        result = self.db.query(
            func.coalesce(func.sum(SignupCredit.amount), 0)
        ).filter(
            SignupCredit.user_id == user_id
        ).scalar()
        
        return Decimal(str(result or 0))
    
    def use_signup_credit(self, user_id: int, amount: Decimal) -> Decimal:
        """
        Use signup credit for a user, returns amount actually used.
        Uses oldest credits first (FIFO).
        """
        if amount <= 0:
            return Decimal("0")
        
        credits = self.db.query(SignupCredit).filter(
            SignupCredit.user_id == user_id,
            SignupCredit.is_active == True
        ).order_by(SignupCredit.created_at.asc()).all()
        
        remaining_to_use = amount
        total_used = Decimal("0")
        
        for credit in credits:
            if remaining_to_use <= 0 or not credit.is_usable:
                continue
                
            available = credit.remaining_amount
            if available <= 0:
                continue
                
            use_amount = min(remaining_to_use, available)
            credit.used_amount += use_amount
            
            total_used += use_amount
            remaining_to_use -= use_amount
            
            self.db.add(credit)
        
        if total_used > 0:
            self.db.commit()
        
        return total_used
