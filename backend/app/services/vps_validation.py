import asyncio
import socket
from typing import Tuple

PERIPHERY_PORT = 8120  # periphery agent port
PERIPHERY_HEALTH_PATH = "/health"  # if available in periphery
PERIPHERY_MIN_VERSION = "0.1.0"


class VPSValidationService:
    async def validate_periphery_client(self, ip_address: str) -> Tuple[bool, str]:
        """
        Attempt to connect to the periphery client on the VPS to validate connectivity.
        Strategy:
        - Try TCP connect to PERIPHERY_PORT with short timeout
        - Optionally try HTTP GET to /health to check version
        """
        # First: TCP connectivity check
        tcp_ok, tcp_msg = await self._tcp_connect(ip_address, PERIPHERY_PORT, timeout=5)
        if not tcp_ok:
            return False, f"Cannot connect to periphery on {ip_address}:{PERIPHERY_PORT} - {tcp_msg}"

        # If we want a stronger check, try a simple HTTP GET to health endpoint
        try:
            import http.client
            conn = http.client.HTTPConnection(ip_address, PERIPHERY_PORT, timeout=5)
            conn.request("GET", PERIPHERY_HEALTH_PATH)
            resp = conn.getresponse()
            if resp.status == 200:
                return True, "Periphery client reachable and healthy"
            else:
                return True, f"Periphery TCP reachable; health endpoint returned {resp.status}"
        except Exception:
            # Health endpoint may not exist; TCP reachability is still OK
            return True, "Periphery client reachable"

    async def _tcp_connect(self, host: str, port: int, timeout: int = 5) -> Tuple[bool, str]:
        loop = asyncio.get_running_loop()
        try:
            fut = loop.getaddrinfo(host, port, type=socket.SOCK_STREAM)
            infos = await asyncio.wait_for(fut, timeout=timeout)
            for family, socktype, proto, canonname, sockaddr in infos:
                try:
                    reader, writer = await asyncio.wait_for(asyncio.open_connection(host=sockaddr[0], port=sockaddr[1]), timeout=timeout)
                    writer.close()
                    await writer.wait_closed()
                    return True, "connected"
                except Exception as e:
                    last_err = str(e)
                    continue
            return False, last_err if 'last_err' in locals() else 'unknown error'
        except Exception as e:
            return False, str(e)

    def _get_package_setup_instructions(self, package: str = None) -> str:
        """Generate package-specific setup instructions for the Docker Compose script."""
        if package in ["Coolify", "Coolify+"]:
            return """##
## COOLIFY SETUP INSTRUCTIONS
## Run these commands BEFORE starting the Docker Compose:
##

# Create the base directories for Coolify under /data/coolify
mkdir -p /data/coolify/{source,ssh,applications,databases,backups,services,proxy,webhooks-during-maintenance}
mkdir -p /data/coolify/ssh/{keys,mux}
mkdir -p /data/coolify/proxy/dynamic

# Generate an SSH key for Coolify to manage your server
ssh-keygen -f /data/coolify/ssh/keys/<EMAIL> -t ed25519 -N '' -C root@coolify

# Add the public key to authorized_keys
cat /data/coolify/ssh/keys/<EMAIL> >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys

# Set the correct permissions for the Coolify files and directories
chown -R 9999:root /data/coolify
chmod -R 700 /data/coolify

# Ensure the Docker network is created
docker network create --attachable coolify

# Create the komodo directory
mkdir -p /etc/komodo

##
## After running the above commands, start the periphery with: docker-compose up -d
##"""
        else:
            return """##
## SETUP INSTRUCTIONS
## Run this command BEFORE starting the Docker Compose:
##

# Create the komodo directory
## mkdir -p /etc/komodo

##
## After creating the directory, start the periphery with: docker-compose up -d
##"""

    def generate_periphery_script(self, package: str = None) -> str:
        """Return a Docker Compose configuration for users to run on their VPS to install and start periphery."""
        from app.core.config import get_settings

        # Get KOMODO_PASSKEY and KOMODO_PROVIDER_IP from backend settings
        settings = get_settings()
        komodo_passkey = settings.KOMODO_PASSKEY
        komodo_provider_ip = settings.KOMODO_PROVIDER_IP

        # Generate package-specific setup instructions
        setup_instructions = self._get_package_setup_instructions(package)

        return f"""####################################
# 🦎 KOMODO COMPOSE - PERIPHERY 🦎 #
####################################

## This compose file will deploy the Komodo Periphery
##
## We rely on Komodo for managing your VPS's Docker containers.
## Learn more about Komodo at https://komo.do/docs/connect-servers#configuration
## You need to make folder for komodo using mkdir -p /etc/komodo or set with PERIPHERY_ROOT_DIRECTORY

## ⚠️  IMPORTANT REQUIREMENTS FOR BYO VPS:
## 1. ENSURE PORT 8120 IS OPEN: Your VPS firewall must allow incoming connections on port 8120
##    This port is required for Manidae to connect to your VPS. Without it, deployment will fail.
## 2. DO NOT CHANGE THE DEFAULT KEY AND IP SETTINGS BELOW: The PERIPHERY_PASSKEYS and
##    PERIPHERY_ALLOWED_IPS values are required for Manidae to connect to your VPS.
##    Changing these values will prevent Manidae from managing your deployment.

{setup_instructions}

services:
  periphery:
    image: ghcr.io/moghtech/komodo-periphery:${{COMPOSE_KOMODO_IMAGE_TAG:-latest}}
    labels:
      komodo.skip: # Prevent Komodo from stopping with StopAllContainers
    restart: unless-stopped
    environment:
      PERIPHERY_ROOT_DIRECTORY: ${{PERIPHERY_ROOT_DIRECTORY:-/etc/komodo}}
      ## Pass the same passkey as used by the Komodo Core connecting to this Periphery agent.
      ## ⚠️  DO NOT CHANGE: Required for Manidae to connect to your VPS
      PERIPHERY_PASSKEYS: {komodo_passkey}
      ## Restrict access to specific IP addresses (Komodo Core server)
      ## ⚠️  DO NOT CHANGE: Required for Manidae to connect to your VPS
      PERIPHERY_ALLOWED_IPS: "{komodo_provider_ip}"
      ## Make server run over https
      PERIPHERY_SSL_ENABLED: true
      ## Specify whether to disable the terminals feature
      ## and disallow remote shell access (inside the Periphery container).
      PERIPHERY_DISABLE_TERMINALS: false
      PERIPHERY_INCLUDE_DISK_MOUNTS: /etc/hostname
    volumes:
      ## Mount external docker socket
      - /var/run/docker.sock:/var/run/docker.sock
      ## Allow Periphery to see processes outside of container
      - /proc:/proc
      - ${{PERIPHERY_ROOT_DIRECTORY:-/etc/komodo}}:${{PERIPHERY_ROOT_DIRECTORY:-/etc/komodo}}
    ## ⚠️  ENSURE PORT 8120 IS OPEN: Your VPS firewall must allow incoming connections on this port
    ports:
      - 8120:8120"""

