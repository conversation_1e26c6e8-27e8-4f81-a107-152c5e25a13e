
import sys
import os
from datetime import datetime, timezone
from decimal import Decimal

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy.orm import Session
from app.db.session import <PERSON>Local
from app.models.transaction import Transaction
from app.models.deployment import Deployment
from app.models.user import User

def fix_user_billing(user_id: int):
    """
    Deletes all 'billing' transactions for a specific user and resets the
    'last_billed_at' timestamp on their active deployments.
    """
    db: Session = SessionLocal()
    try:
        # Find the user
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            print(f"User with ID {user_id} not found.")
            return

        # Find all billing transactions for the user
        transactions_to_delete = db.query(Transaction).filter(
            Transaction.user_id == user_id,
            Transaction.type == 'billing'
        ).all()

        if not transactions_to_delete:
            print(f"No billing transactions found for user {user_id}.")
        else:
            print(f"Found {len(transactions_to_delete)} billing transactions to delete.")
            for tx in transactions_to_delete:
                # Revert the balance change from this transaction
                user.balance = (user.balance or Decimal('0')) - tx.amount
                db.delete(tx)
            print("Deleted transactions and reverted balance changes.")

        # Find all active deployments for the user and reset billing timestamp
        active_deployments = db.query(Deployment).filter(
            Deployment.user_id == user_id,
            Deployment.status == 'ACTIVE'
        ).all()

        if active_deployments:
            print(f"Found {len(active_deployments)} active deployments. Resetting 'last_billed_at'.")
            for dep in active_deployments:
                dep.last_billed_at = None
        
        db.add(user)
        db.commit()
        print(f"Successfully reset billing state for user {user_id}.")
        db.refresh(user)
        print(f"User's new balance is: {user.balance:.2f} EUR")

    except Exception as e:
        db.rollback()
        print(f"An error occurred: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    # The user ID is 1 based on the SQL dump provided earlier
    USER_ID_TO_FIX = 1
    print(f"Starting billing fix for user ID: {USER_ID_TO_FIX}...")
    fix_user_billing(USER_ID_TO_FIX)
    print("Fix complete.")
