from sqlalchemy.orm import Session
from decimal import Decimal
from datetime import datetime, timedelta
from typing import List, Tuple

from app.models.user import User
from app.models.referral_credit import ReferralCredit
from app.models.transaction import Transaction
from app.core.config import get_settings

class ReferralService:
    """Service for managing referral credits and calculations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.settings = get_settings()
    
    def create_referral_credits(self, referrer: User, referee: User, ip_address: str = None) -> Tuple[ReferralCredit, ReferralCredit]:
        """
        Create referral credits for both referrer and referee.
        Referrer only gets credit if they have minimum balance to prevent abuse.
        Referee always gets credit as a welcome bonus.

        Args:
            referrer: The user who made the referral
            referee: The new user who was referred
            ip_address: IP address of the referee when signing up

        Returns:
            Tuple of (referrer_credit, referee_credit) - referrer_credit may be None
        """
        now = datetime.utcnow()

        # Calculate expiry date if configured
        expires_at = None
        if self.settings.REFERRAL_CREDIT_EXPIRY_DAYS > 0:
            expires_at = now + timedelta(days=self.settings.REFERRAL_CREDIT_EXPIRY_DAYS)

        # Check if referrer has minimum balance to earn credit
        from app.services.billing_service import BillingService
        billing_service = BillingService(self.db)
        referrer_balance_info = billing_service.get_user_total_balance(referrer)
        referrer_qualifies = referrer_balance_info["total_balance"] >= Decimal(str(self.settings.MIN_BALANCE_TO_DEPLOY))

        # Create credit for referrer only if they have minimum balance
        referrer_credit = None
        if referrer_qualifies:
            referrer_credit = ReferralCredit(
                user_id=referrer.id,
                amount=Decimal(str(self.settings.REFERRER_CREDIT_AMOUNT)),
                credit_type="referrer",
                referral_user_id=referee.id,
                expires_at=expires_at,
                used_amount=Decimal("0"),
                is_active=True,
                ip_address=ip_address
            )
            self.db.add(referrer_credit)

        # Create credit for referee (always give referee credit as welcome bonus)
        referee_credit = ReferralCredit(
            user_id=referee.id,
            amount=Decimal(str(self.settings.REFERRAL_CREDIT_AMOUNT)),
            credit_type="referee",
            referral_user_id=referrer.id,
            expires_at=expires_at,
            used_amount=Decimal("0"),
            is_active=True,
            ip_address=ip_address
        )
        self.db.add(referee_credit)

        # Create transaction records
        transactions = []

        # Only create referrer transaction if they qualified for credit
        if referrer_credit:
            referrer_transaction = Transaction(
                user_id=referrer.id,
                amount=Decimal(str(self.settings.REFERRER_CREDIT_AMOUNT)),
                type="referral_credit",
                description=f"Referral credit for referring {referee.username}"
            )
            transactions.append(referrer_transaction)

        # Always create referee transaction
        referee_transaction = Transaction(
            user_id=referee.id,
            amount=Decimal(str(self.settings.REFERRAL_CREDIT_AMOUNT)),
            type="referral_credit",
            description=f"Referral credit from {referrer.username}"
        )
        transactions.append(referee_transaction)

        # Add all transactions
        for transaction in transactions:
            self.db.add(transaction)
        
        self.db.commit()

        # Refresh objects that were created
        if referrer_credit:
            self.db.refresh(referrer_credit)
        self.db.refresh(referee_credit)

        return referrer_credit, referee_credit
    
    def get_available_credits(self, user_id: int) -> List[ReferralCredit]:
        """
        Get all available (usable) referral credits for a user.
        
        Args:
            user_id: The user's ID
            
        Returns:
            List of usable ReferralCredit objects
        """
        credits = self.db.query(ReferralCredit).filter(
            ReferralCredit.user_id == user_id,
            ReferralCredit.is_active == True
        ).all()
        
        # Filter to only usable credits
        return [credit for credit in credits if credit.is_usable]
    
    def calculate_total_available_credit(self, user_id: int) -> Decimal:
        """
        Calculate total available referral credit for a user.
        
        Args:
            user_id: The user's ID
            
        Returns:
            Total available credit amount
        """
        available_credits = self.get_available_credits(user_id)
        return sum(credit.remaining_amount for credit in available_credits)
    
    def use_referral_credits(self, user_id: int, amount_needed: Decimal) -> Decimal:
        """
        Use referral credits to cover a charge, oldest first.
        
        Args:
            user_id: The user's ID
            amount_needed: Amount to cover with referral credits
            
        Returns:
            Amount actually covered by referral credits
        """
        available_credits = self.get_available_credits(user_id)
        
        # Sort by creation date (oldest first)
        available_credits.sort(key=lambda x: x.created_at)
        
        amount_covered = Decimal("0")
        remaining_needed = amount_needed
        
        for credit in available_credits:
            if remaining_needed <= 0:
                break
                
            available_amount = credit.remaining_amount
            amount_to_use = min(available_amount, remaining_needed)
            
            if amount_to_use > 0:
                credit.used_amount += amount_to_use
                amount_covered += amount_to_use
                remaining_needed -= amount_to_use
                
                # Create transaction record for credit usage
                transaction = Transaction(
                    user_id=user_id,
                    amount=-amount_to_use,  # Negative because it's a charge
                    type="referral_credit_usage",
                    description=f"Used referral credit (ID: {credit.id})"
                )
                self.db.add(transaction)
                self.db.add(credit)
        
        if amount_covered > 0:
            self.db.commit()
        
        return amount_covered
    
    def expire_old_credits(self) -> int:
        """
        Mark expired credits as inactive.
        
        Returns:
            Number of credits that were expired
        """
        now = datetime.utcnow()
        
        expired_credits = self.db.query(ReferralCredit).filter(
            ReferralCredit.is_active == True,
            ReferralCredit.expires_at.isnot(None),
            ReferralCredit.expires_at < now
        ).all()
        
        count = 0
        for credit in expired_credits:
            credit.is_active = False
            self.db.add(credit)
            count += 1
        
        if count > 0:
            self.db.commit()
        
        return count
    
    def get_user_balance_with_credits(self, user: User) -> Tuple[Decimal, Decimal, Decimal]:
        """
        Get user's total balance including referral credits.
        
        Args:
            user: User object
            
        Returns:
            Tuple of (regular_balance, referral_credits, total_balance)
        """
        regular_balance = user.balance or Decimal("0")
        referral_credits = self.calculate_total_available_credit(user.id)
        total_balance = regular_balance + referral_credits
        
        return regular_balance, referral_credits, total_balance

    def use_referral_credit(self, user_id: int, amount: Decimal) -> Decimal:
        """
        Use referral credit for a user, returns amount actually used.
        Uses oldest credits first (FIFO).

        Args:
            user_id: The user's ID
            amount: Amount to use

        Returns:
            Amount actually used from referral credits
        """
        if amount <= 0:
            return Decimal("0")

        # Get usable credits ordered by creation date (oldest first)
        credits = self.db.query(ReferralCredit).filter(
            ReferralCredit.user_id == user_id,
            ReferralCredit.is_active == True
        ).order_by(ReferralCredit.created_at.asc()).all()

        remaining_to_use = amount
        total_used = Decimal("0")

        for credit in credits:
            if remaining_to_use <= 0 or not credit.is_usable:
                continue

            available = credit.remaining_amount
            if available <= 0:
                continue

            use_amount = min(remaining_to_use, available)
            credit.used_amount += use_amount

            total_used += use_amount
            remaining_to_use -= use_amount

            self.db.add(credit)

        if total_used > 0:
            self.db.commit()

        return total_used
