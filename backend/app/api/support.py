from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, EmailStr
from app.services.email_service import send_support_request_email, send_referral_request_email
from app.core.config import get_settings

router = APIRouter()

class SupportRequest(BaseModel):
    name: str
    email: EmailStr
    subject: str
    message: str

class ReferralRequest(BaseModel):
    email: EmailStr
    reason: str

@router.post("/contact")
def submit_support_request(request: SupportRequest):
    """Submit a support request via email"""
    settings = get_settings()
    
    if not settings.USE_EMAIL:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Email service is not available"
        )
    
    # Send support email
    success = send_support_request_email(
        user_name=request.name,
        user_email=request.email,
        subject=request.subject,
        message=request.message
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send support request"
        )
    
    return {"message": "Support request sent successfully"}

@router.post("/referral-request")
def submit_referral_request(request: ReferralRequest):
    """Submit a referral credit request via email"""
    settings = get_settings()

    if not settings.USE_EMAIL:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Email service is not available"
        )

    # Send referral request email
    success = send_referral_request_email(
        user_email=request.email,
        reason=request.reason
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send referral request"
        )

    return {"message": "Referral request sent successfully"}
