from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, IPvAnyAddress
from sqlalchemy.orm import Session
from typing import List
from app.core.security import verify_api_key
from app.api.auth import get_current_user
from app.schemas.user import User
from app.db.session import SessionLocal
from app.models.deployment import Deployment as DeploymentModel
from app.services.vps_validation import VPSValidationService

router = APIRouter(dependencies=[Depends(verify_api_key)])


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class ServerInfo(BaseModel):
    id: int
    name: str | None
    ip_address: str | None
    provider: str | None
    region: str | None
    instance_type: str | None
    status: str


class ValidateVPSRequest(BaseModel):
    ip_address: IPvAnyAddress


class ValidateVPSResponse(BaseModel):
    status: str
    message: str


@router.get("/", response_model=List[ServerInfo])
def list_servers(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    # Reuse existing deployments as servers for now
    deployments = (
        db.query(DeploymentModel)
        .filter(DeploymentModel.user_id == current_user.id)
        .filter(DeploymentModel.status == "ACTIVE")
        .all()
    )
    servers: List[ServerInfo] = []
    for d in deployments:
        ip = d.komodo_host_ip or d.vps_ip_address
        servers.append(
            ServerInfo(
                id=d.id,
                name=d.client_name,
                ip_address=ip,
                provider=d.cloud_provider,
                region=d.region,
                instance_type=d.instance_type,
                status=d.status,
            )
        )
    return servers


validation_service = VPSValidationService()


@router.post("/validate-vps", response_model=ValidateVPSResponse)
async def validate_vps(req: ValidateVPSRequest) -> ValidateVPSResponse:
    ok, msg = await validation_service.validate_periphery_client(str(req.ip_address))
    if ok:
        return ValidateVPSResponse(status="success", message=msg)
    raise HTTPException(status_code=400, detail=msg)


@router.get("/periphery-script", response_model=dict)
def get_periphery_script(package: str = None):
    script = validation_service.generate_periphery_script(package)
    return {"script": script}

