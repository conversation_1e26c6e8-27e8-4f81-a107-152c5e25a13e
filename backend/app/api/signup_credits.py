from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from decimal import Decimal
from datetime import datetime
from typing import List, Optional

from app.db.session import get_db
from app.models.user import User
from app.models.signup_credit import SignupCredit
from app.api.auth import get_current_user
from app.services.signup_credit_service import SignupCreditService

router = APIRouter()

class SignupCreditResponse(BaseModel):
    id: int
    amount: float
    expires_at: Optional[datetime]
    used_amount: float
    remaining_amount: float
    is_active: bool
    is_expired: bool
    is_usable: bool
    created_at: datetime

@router.get("/signup-credits", response_model=List[SignupCreditResponse])
async def get_my_signup_credits(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user's signup credits"""
    signup_credit_service = SignupCreditService(db)
    credits = signup_credit_service.get_user_signup_credits(current_user.id)
    
    return [
        SignupCreditResponse(
            id=credit.id,
            amount=float(credit.amount),
            expires_at=credit.expires_at,
            used_amount=float(credit.used_amount),
            remaining_amount=float(credit.remaining_amount),
            is_active=credit.is_active,
            is_expired=credit.is_expired,
            is_usable=credit.is_usable,
            created_at=credit.created_at
        )
        for credit in credits
    ]
