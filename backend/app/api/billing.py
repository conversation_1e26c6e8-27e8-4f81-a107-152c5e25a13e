from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Optional
from collections import defaultdict
from datetime import date, datetime, timedelta
from pydantic import BaseModel
from decimal import Decimal

from app.db.session import get_db
from app.models.transaction import Transaction
from app.schemas.transaction import Transaction as TransactionSchema
from app.schemas.billing import BalanceAdjustment
from app.models.user import User
from app.api.auth import get_current_user, get_admin_user

router = APIRouter()

class GroupedTransactions(BaseModel):
    date: date
    transactions: List[TransactionSchema]

class PaginatedTransactions(BaseModel):
    transactions: List[GroupedTransactions]
    total_pages: int
    current_page: int
    has_next: bool
    has_previous: bool


@router.get("/transactions", response_model=PaginatedTransactions)
def read_user_transactions(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    days_per_page: int = Query(30, ge=1, le=365, description="Number of days per page"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Retrieve and group transactions for the current user by date with pagination.
    """
    # Calculate date range for this page
    end_date = datetime.utcnow().date()
    page_end_date = end_date - timedelta(days=((page - 1) * days_per_page))
    page_start_date = end_date - timedelta(days=(page * days_per_page))

    # Get transactions for the current page date range
    # Include the end date by using <= instead of <
    transactions = db.query(Transaction).filter(
        Transaction.user_id == current_user.id,
        Transaction.created_at >= datetime.combine(page_start_date, datetime.min.time()),
        Transaction.created_at <= datetime.combine(page_end_date, datetime.max.time())
    ).order_by(Transaction.created_at.desc()).all()

    # Group transactions by date
    grouped: Dict[date, List[TransactionSchema]] = defaultdict(list)
    for tx in transactions:
        grouped[tx.created_at.date()].append(TransactionSchema.from_orm(tx))

    # Sort dates descending
    sorted_dates = sorted(grouped.keys(), reverse=True)
    grouped_transactions = [GroupedTransactions(date=day, transactions=grouped[day]) for day in sorted_dates]

    # Calculate pagination info
    # Get the earliest transaction date to determine total pages
    earliest_tx = db.query(Transaction).filter(Transaction.user_id == current_user.id).order_by(Transaction.created_at.asc()).first()
    if earliest_tx:
        earliest_date = earliest_tx.created_at.date()
        total_days = (end_date - earliest_date).days + 1
        total_pages = max(1, (total_days + days_per_page - 1) // days_per_page)
    else:
        total_pages = 1

    return PaginatedTransactions(
        transactions=grouped_transactions,
        total_pages=total_pages,
        current_page=page,
        has_next=page < total_pages,
        has_previous=page > 1
    )

@router.get("/admin/transactions", response_model=PaginatedTransactions)
def read_admin_transactions(
    user: Optional[str] = Query(None, description="Filter by username"),
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    days_per_page: int = Query(30, ge=1, le=365, description="Number of days per page"),
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user),
):
    """
    Retrieve and group transactions for admin view, optionally filtered by user, with pagination.
    """
    # Calculate date range for this page
    end_date = datetime.utcnow().date()
    page_end_date = end_date - timedelta(days=((page - 1) * days_per_page))
    page_start_date = end_date - timedelta(days=(page * days_per_page))

    query = db.query(Transaction).filter(
        Transaction.created_at >= datetime.combine(page_start_date, datetime.min.time()),
        Transaction.created_at <= datetime.combine(page_end_date, datetime.max.time())
    )

    if user:
        # Join with User table to filter by username
        query = query.join(User).filter(User.username == user)

    transactions = query.order_by(Transaction.created_at.desc()).all()

    # Group transactions by date
    grouped: Dict[date, List[TransactionSchema]] = defaultdict(list)
    for tx in transactions:
        grouped[tx.created_at.date()].append(TransactionSchema.from_orm(tx))

    # Sort dates descending
    sorted_dates = sorted(grouped.keys(), reverse=True)
    grouped_transactions = [GroupedTransactions(date=day, transactions=grouped[day]) for day in sorted_dates]

    # Calculate pagination info
    # Get the earliest transaction date to determine total pages
    earliest_query = db.query(Transaction)
    if user:
        earliest_query = earliest_query.join(User).filter(User.username == user)
    earliest_tx = earliest_query.order_by(Transaction.created_at.asc()).first()

    if earliest_tx:
        earliest_date = earliest_tx.created_at.date()
        total_days = (end_date - earliest_date).days + 1
        total_pages = max(1, (total_days + days_per_page - 1) // days_per_page)
    else:
        total_pages = 1

    return PaginatedTransactions(
        transactions=grouped_transactions,
        total_pages=total_pages,
        current_page=page,
        has_next=page < total_pages,
        has_previous=page > 1
    )

@router.post("/admin/users/{user_id}/adjust-balance", response_model=TransactionSchema)
def adjust_user_balance(
    user_id: int,
    adjustment: BalanceAdjustment,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user),
):
    """
    Manually adjust a user's balance. Admin only.
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user.balance = (user.balance or Decimal("0")) + adjustment.amount

    tx = Transaction(
        user_id=user.id,
        amount=adjustment.amount,
        type="manual_adjustment",
        description=adjustment.description
    )

    db.add(user)
    db.add(tx)
    db.commit()
    db.refresh(tx)

    return tx
