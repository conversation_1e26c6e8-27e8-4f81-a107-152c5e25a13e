from sqlalchemy import Column, String, Integer, Float, <PERSON>olean
from app.db.base import BaseModel

class InstancePricing(BaseModel):
    __tablename__ = "instance_pricing"

    cloud_provider = Column(String)
    instance_type = Column(String)
    region = Column(String)
    cost_per_hour = Column(Float)
    package_multiplier_pangolin = Column(Float)
    package_multiplier_pangolin_plus = Column(Float)
    package_multiplier_pangolin_plus_ai = Column(Float)
    package_multiplier_coolify = Column(Float)
    support_cost_level1 = Column(Float)
    support_cost_level2 = Column(Float)
    support_cost_level3 = Column(Float)
    active = Column(Boolean, default=True)
    cpu = Column(Float)
    memory = Column(Float)
    country_code = Column(String)
