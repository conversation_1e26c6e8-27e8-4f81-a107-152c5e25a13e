from sqlalchemy import Column, Integer, String, Numeric, ForeignKey
from sqlalchemy.orm import relationship
from app.db.base import BaseModel

class Transaction(BaseModel):
    __tablename__ = "transactions"

    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    amount = Column(Numeric(12, 2), nullable=False)  # positive for payments, negative for charges
    type = Column(String, nullable=False)  # 'billing', 'manual_adjustment', 'payment'
    description = Column(String, nullable=True)

    user = relationship("User")

