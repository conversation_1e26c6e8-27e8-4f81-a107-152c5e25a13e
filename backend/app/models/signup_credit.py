from sqlalchemy import Column, Integer, Numeric, String, <PERSON><PERSON><PERSON>, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app.db.base import BaseModel
from decimal import Decimal
from datetime import datetime


class SignupCredit(BaseModel):
    __tablename__ = "signup_credits"

    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    amount = Column(Numeric(12, 2), nullable=False)
    expires_at = Column(DateTime, nullable=True)  # NULL means no expiry
    used_amount = Column(Numeric(12, 2), nullable=False, default=0)
    is_active = Column(Boolean, nullable=False, default=True)

    # Relationships
    user = relationship("User", overlaps="signup_credits")

    @property
    def remaining_amount(self) -> Decimal:
        """Calculate remaining credit amount"""
        return self.amount - self.used_amount

    @property
    def is_expired(self) -> bool:
        """Check if credit has expired"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at

    @property
    def is_usable(self) -> bool:
        """Check if credit can be used (active, not expired, has remaining amount)"""
        return (
            self.is_active and 
            not self.is_expired and 
            self.remaining_amount > 0
        )
