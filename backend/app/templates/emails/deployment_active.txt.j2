Hi {{ username }},

🎉 Your VPS is now LIVE and ACTIVE! Billing has started.

Deployment Details:
- ID: Deployment {{ deployment.id }}
- Package: {{ deployment.package }}
- Cloud: {{ deployment.cloud_provider }}
- Region: {{ deployment.region }}
- Instance Type: {{ deployment.instance_type }}
- Instance IP: {{ deployment.instance_ip }}
- Daily Cost: {{ deployment.daily_cost_eur }}
- Created At: {{ deployment.created_at }}

{% if deployment.domain %}
🔧 NEXT STEPS - DNS Configuration Required:

Your domain "{{ deployment.domain }}" needs to be configured to point to your VPS:

1. Log into your DNS provider (Cloudflare, etc.)
2. Create an A record for "{{ deployment.domain }}" pointing to: {{ deployment.instance_ip }}
{% if deployment.admin_subdomain %}3. Create an A record for "{{ deployment.admin_subdomain }}.{{ deployment.domain }}" pointing to: {{ deployment.instance_ip }}{% endif %}
4. Wait for DNS propagation (usually 5-15 minutes)
5. Return to your dashboard and click "Retry DNS Validation"

⏰ Please be patient after DNS validation succeeds - SSL certificates may take a few minutes to generate and propagate.

💡 Your VPS is running and you're being billed even if DNS isn't configured yet. Please complete the DNS setup to access your services.
{% else %}
✅ No DNS configuration required - your deployment is ready to use!
{% endif %}

🔗 Manage your deployment: Log in to your dashboard for more options.

Thanks,
Manidae Cloud

