<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>VPS is LIVE - Action Required</title>
    <style>
      body { font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif; color: #111827; line-height: 1.6; }
      .container { max-width: 600px; margin: 0 auto; padding: 24px; }
      .label { color: #6b7280; font-weight: 500; }
      .val { color: #111827; }
      .item { margin-bottom: 8px; }
      .success { color: #059669; font-weight: 600; }
      .warning { color: #d97706; font-weight: 600; }
      .info { color: #2563eb; font-weight: 600; }
      .dns-steps { background: #f3f4f6; padding: 16px; border-radius: 8px; margin: 16px 0; }
      .dns-steps ol { margin: 8px 0; padding-left: 20px; }
      .dns-steps li { margin-bottom: 8px; }
      .ip-highlight { background: #fef3c7; padding: 2px 6px; border-radius: 4px; font-family: monospace; font-weight: 600; }
      .important { background: #fef2f2; border-left: 4px solid #ef4444; padding: 12px; margin: 16px 0; }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>🎉 <span class="success">Your VPS is now LIVE and ACTIVE!</span></h2>
      <p><span class="warning">Billing has started.</span></p>

      <h3>Deployment Details:</h3>
      <div class="item"><span class="label">ID:</span> <span class="val">Deployment {{ deployment.id }}</span></div>
      <div class="item"><span class="label">Package:</span> <span class="val">{{ deployment.package }}</span></div>
      <div class="item"><span class="label">Cloud:</span> <span class="val">{{ deployment.cloud_provider }}</span></div>
      <div class="item"><span class="label">Region:</span> <span class="val">{{ deployment.region }}</span></div>
      <div class="item"><span class="label">Instance Type:</span> <span class="val">{{ deployment.instance_type }}</span></div>
      <div class="item"><span class="label">Instance IP:</span> <span class="val ip-highlight">{{ deployment.instance_ip }}</span></div>
      <div class="item"><span class="label">Daily Cost:</span> <span class="val">{{ deployment.daily_cost_eur }}</span></div>
      <div class="item"><span class="label">Created At:</span> <span class="val">{{ deployment.created_at }}</span></div>

      {% if deployment.domain %}
      <div class="dns-steps">
        <h3>🔧 <span class="info">NEXT STEPS - DNS Configuration Required:</span></h3>
        <p>Your domain <strong>"{{ deployment.domain }}"</strong> needs to be configured to point to your VPS:</p>

        <ol>
          <li>Log into your DNS provider (Cloudflare, etc.)</li>
          <li>Create an A record for <strong>"{{ deployment.domain }}"</strong> pointing to: <span class="ip-highlight">{{ deployment.instance_ip }}</span></li>
          {% if deployment.admin_subdomain %}<li>Create an A record for <strong>"{{ deployment.admin_subdomain }}.{{ deployment.domain }}"</strong> pointing to: <span class="ip-highlight">{{ deployment.instance_ip }}</span></li>{% endif %}
          <li>Wait for DNS propagation (usually 5-15 minutes)</li>
          <li>Return to your dashboard and click <strong>"Retry DNS Validation"</strong></li>
        </ol>

        <p><span class="info">⏰ Please be patient after DNS validation succeeds</span> - SSL certificates may take a few minutes to generate and propagate.</p>
      </div>

      <div class="important">
        <p><strong>💡 Important:</strong> Your VPS is running and you're being billed even if DNS isn't configured yet. Please complete the DNS setup to access your services.</p>
      </div>
      {% else %}
      <div class="dns-steps">
        <p><span class="success">✅ No DNS configuration required - your deployment is ready to use!</span></p>
      </div>
      {% endif %}

      <p><strong>🔗 Manage your deployment:</strong> Log in to your dashboard for more options and monitoring.</p>

      <p>Thanks,<br>Manidae Cloud</p>
    </div>
  </body>
</html>

