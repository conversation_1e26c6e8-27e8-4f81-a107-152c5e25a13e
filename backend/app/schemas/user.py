from pydantic import BaseModel
from decimal import Decimal
from datetime import datetime

class UserBase(BaseModel):
    username: str
    email: str

class UserCreate(UserBase):
    password: str
    referral_code: str | None = None

class User(UserBase):
    id: int
    is_admin: bool
    balance: Decimal | float = 0
    is_verified: bool = False
    verified_at: datetime | None = None

    class Config:
        from_attributes = True
        json_encoders = {Decimal: lambda v: float(v)}

class UserCreateAdmin(UserBase):
    password: str
    is_admin: bool = False
