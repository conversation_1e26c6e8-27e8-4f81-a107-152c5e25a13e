from pydantic import model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv
import os
from typing import Optional, Any

load_dotenv()

class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",
        extra="ignore"
    )
    db_user: str
    db_password: str
    db_name: str
    db_host: str = "localhost"
    db_port: int = 5432

    DATABASE_URL: Optional[str] = None

    @model_validator(mode='before')
    def assemble_db_connection(cls, v: dict[str, Any]) -> dict[str, Any]:
        if 'DATABASE_URL' not in v:
            v['DATABASE_URL'] = (
                f"postgresql://{v.get('db_user')}:{v.get('db_password')}@"
                f"{v.get('db_host', 'localhost')}:{v.get('db_port', 5432)}/{v.get('db_name')}"
            )
        return v

    GCP_PROJECT_ID: str = os.getenv("GCP_PROJECT_ID", "")
    GCP_CREDENTIALS_FILE: str = os.getenv("GCP_CREDENTIALS_FILE", "")
    KOMODO_PROVIDER_ENDPOINT: str = os.getenv("KOMODO_PROVIDER_ENDPOINT", "")
    KOMODO_API_KEY: str = os.getenv("KOMODO_API_KEY", "")
    KOMODO_API_SECRET: str = os.getenv("KOMODO_API_SECRET", "")
    KOMODO_PASSKEY: str = os.getenv("KOMODO_PASSKEY", "your_komodo_passkey")
    KOMODO_PROVIDER_IP: str = os.getenv("KOMODO_PROVIDER_IP", "")
    GITHUB_TOKEN: str = os.getenv("GITHUB_TOKEN", "")
    HCLOUD_TOKEN: str = os.getenv("HCLOUD_TOKEN", "")
    LINODE_TOKEN: str = os.getenv("LINODE_TOKEN", "")
    LINODE_ROOT_PASSWORD: Optional[str] = os.getenv("LINODE_ROOT_PASSWORD")
    VULTR_API_KEY: str = os.getenv("VULTR_API_KEY", "")
    DIGITALOCEAN_TOKEN: str = os.getenv("DIGITALOCEAN_TOKEN", "")
    SSH_PUBLIC_KEY: Optional[str] = os.getenv("SSH_PUBLIC_KEY")
    SSH_PRIVATE_KEY_PATH: Optional[str] = os.getenv("SSH_PRIVATE_KEY_PATH")
    SHARED_SSH_PUBLIC_KEY: Optional[str] = os.getenv("SHARED_SSH_PUBLIC_KEY")

    APP_ENV: Optional[str]  = os.getenv("APP_ENV", "production")

    # Provider-specific SSH key files
    HETZNER_SSH_FILE: Optional[str] = os.getenv("HETZNER_SSH_FILE")
    GCP_SSH_FILE: Optional[str] = os.getenv("GCP_SSH_FILE")
    LINODE_SSH_FILE: Optional[str] = os.getenv("LINODE_SSH_FILE")
    VULTR_SSH_FILE: Optional[str] = os.getenv("VULTR_SSH_FILE")
    DIGITALOCEAN_SSH_FILE: Optional[str] = os.getenv("DIGITALOCEAN_SSH_FILE")
    AWSLIGHTSAIL_SSH_FILE: Optional[str] = os.getenv("AWSLIGHTSAIL_SSH_FILE")
    AZURE_SSH_FILE: Optional[str] = os.getenv("AZURE_SSH_FILE")

    # SSH key strategy configuration for each provider
    # SHARED_KEY_REFERENCE: Reference pre-provisioned shared SSH key (global providers)
    # ENV_KEY_PER_DEPLOYMENT: Create new SSH key resource per deployment using env key (regional providers)
    SSH_KEY_STRATEGIES: dict = {
        "DigitalOcean": "SHARED_KEY_REFERENCE",
        "Vultr": "SHARED_KEY_REFERENCE",
        "Hetzner": "SHARED_KEY_REFERENCE",
        "Linode": "SHARED_KEY_REFERENCE",
        "AWS Lightsail": "ENV_KEY_PER_DEPLOYMENT",
        "Azure": "ENV_KEY_PER_DEPLOYMENT",
        "Google Cloud": "ENV_KEY_PER_DEPLOYMENT",
    }

    def get_ssh_key_strategy(self, provider_name: str) -> str:
        """Get the SSH key strategy for a given cloud provider."""
        return self.SSH_KEY_STRATEGIES.get(provider_name, "SHARED_KEY_REFERENCE")

    def get_provider_ssh_key(self, provider: str) -> str:
        """Get SSH public key content for a specific cloud provider from file."""
        ssh_file_mapping = {
            "Hetzner": self.HETZNER_SSH_FILE,
            "Google Cloud": self.GCP_SSH_FILE,
            "Linode": self.LINODE_SSH_FILE,
            "Vultr": self.VULTR_SSH_FILE,
            "DigitalOcean": self.DIGITALOCEAN_SSH_FILE,
            "AWS Lightsail": self.AWSLIGHTSAIL_SSH_FILE,
            "Azure": self.AZURE_SSH_FILE,
        }

        ssh_file_path = ssh_file_mapping.get(provider)
        if not ssh_file_path:
            raise ValueError(f"No SSH file configured for provider: {provider}")

        try:
            with open(ssh_file_path, 'r') as f:
                ssh_key = f.read().strip()
                if not ssh_key:
                    raise ValueError(f"SSH file is empty: {ssh_file_path}")
                return ssh_key
        except FileNotFoundError:
            raise ValueError(f"SSH file not found: {ssh_file_path}")
        except Exception as e:
            raise ValueError(f"Error reading SSH file {ssh_file_path}: {e}")

    AWS_ACCESS_KEY_ID: Optional[str] = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY: Optional[str] = os.getenv("AWS_SECRET_ACCESS_KEY")

    AZURE_SUBSCRIPTION_ID: str = os.getenv("AZURE_SUBSCRIPTION_ID", "")
    AZURE_CLIENT_ID: str = os.getenv("AZURE_CLIENT_ID", "")
    AZURE_CLIENT_SECRET: str = os.getenv("AZURE_CLIENT_SECRET", "")
    AZURE_TENANT_ID: str = os.getenv("AZURE_TENANT_ID", "")

    SECRET_KEY: str = os.getenv("SECRET_KEY", "a_very_secret_key")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days

    API_SECRET_KEY: str = os.getenv("API_SECRET_KEY", "")

    # Billing configuration
    MIN_BALANCE_TO_DEPLOY: float = float(os.getenv("MIN_BALANCE_TO_DEPLOY", "0.00"))
    BILLING_INTERVAL_MINUTES: int = int(os.getenv("BILLING_INTERVAL_MINUTES", "60"))
    BILLING_BYOVPS_DAILY_FEE: float = float(os.getenv("BILLING_BYOVPS_DAILY_FEE", "0.12"))

    # Stripe configuration (optional)
    STRIPE_SECRET_KEY: str = os.getenv("STRIPE_SECRET_KEY", "")
    STRIPE_WEBHOOK_SECRET: str = os.getenv("STRIPE_WEBHOOK_SECRET", "")

    # Signup credit system configuration
    SIGNUP_CREDIT_AMOUNT: float = float(os.getenv("SIGNUP_CREDIT_AMOUNT", "0.00"))  # Credit for new signup
    SIGNUP_CREDIT_EXPIRY_DAYS: int = int(os.getenv("SIGNUP_CREDIT_EXPIRY_DAYS", "30"))  # 0 = no expiry

    # Frontend URL configuration
    FRONTEND_BASE_URL: str = os.getenv("FRONTEND_BASE_URL", "http://localhost:3000")
    CORS_ORIGINS: str = os.getenv("CORS_ORIGINS", "http://localhost:3000")

    # Referral system configuration
    REFERRAL_CREDIT_AMOUNT: float = float(os.getenv("REFERRAL_CREDIT_AMOUNT", "5.00"))  # Credit for new user
    REFERRER_CREDIT_AMOUNT: float = float(os.getenv("REFERRER_CREDIT_AMOUNT", "5.00"))  # Credit for referrer
    REFERRAL_CREDIT_EXPIRY_DAYS: int = int(os.getenv("REFERRAL_CREDIT_EXPIRY_DAYS", "30"))  # 0 = no expiry
    REFERRAL_BASE_URL: str = os.getenv("REFERRAL_BASE_URL", "http://localhost:3000")


    # Email configuration / feature flag
    USE_EMAIL: bool = os.getenv("USE_EMAIL", "false").lower() in ("true", "1", "yes")
    MAILGUN_API_URL: str = os.getenv("MAILGUN_API_URL", "")
    MAILGUN_API_KEY: str = os.getenv("MAILGUN_API_KEY", "")
    EMAIL_FROM: str = os.getenv("EMAIL_FROM", "Manidae Cloud <<EMAIL>>")
    SUPPORT_EMAIL: str = os.getenv("SUPPORT_EMAIL", "<EMAIL>")
    # Where the email link should send users; use frontend route
    VERIFICATION_BASE_URL: str = os.getenv("VERIFICATION_BASE_URL", "http://127.0.0.1:5173/verify-email")
    PASSWORD_RESET_BASE_URL: str = os.getenv("PASSWORD_RESET_BASE_URL", "http://127.0.0.1:5173/reset-password")

    # Terraform configuration
    TERRAFORM_DRY_RUN: bool = os.getenv("TERRAFORM_DRY_RUN", "false").lower() in ("true", "1", "yes")


cached_settings: Optional[Settings] = None

def get_settings(env_values: Optional[dict[Any, Any]] = None) -> Settings:
    global cached_settings
    if cached_settings is None:
        load_dotenv()
        if env_values:
            cached_settings = Settings(**env_values)
        else:
            cached_settings = Settings(
                db_user=os.getenv("DB_USER", ""),
                db_password=os.getenv("DB_PASSWORD", ""),
                db_name=os.getenv("DB_NAME", ""),
            )
    return cached_settings

def reload_settings() -> Settings:
    global cached_settings
    cached_settings = None
    return get_settings()
