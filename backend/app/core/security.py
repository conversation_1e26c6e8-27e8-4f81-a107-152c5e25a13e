from datetime import datetime, timedelta
from typing import Any, Union, Optional

from jose import jwt
from passlib.context import <PERSON><PERSON><PERSON><PERSON>xt
from fastapi import H<PERSON>P<PERSON>x<PERSON>, Depends
from fastapi.security import APIKeyHeader

from app.core.config import get_settings

pwd_context = CryptContext(schemes=["argon2"], deprecated="auto")

ALGORITHM = "HS256"

# Simple API key header checker
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)

async def verify_api_key(api_key: str = Depends(api_key_header)):
    if not get_settings().API_SECRET_KEY:
        return True  # Skip check if not configured
    
    if api_key != get_settings().API_SECRET_KEY:
        raise HTTPException(status_code=403, detail="Invalid API key")
    return True


def create_access_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=get_settings().ACCESS_TOKEN_EXPIRE_MINUTES
        )
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(to_encode, get_settings().SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)
