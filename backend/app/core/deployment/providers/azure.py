import os
from pathlib import Path
from jinja2 import Environment, FileSystemLoader
from app.core.deployment.providers.base import ProviderStrategy
from app.models.deployment import Deployment as DeploymentModel
from app.core.config import get_settings
from app.core.deployment.package_utils import normalize_package_name_for_hostname

class AzureStrategy(ProviderStrategy):
    """Concrete strategy for Google Cloud deployments."""

    def generate_terraform_files(self, deployment_dir: Path, deployment: DeploymentModel) -> None:
        """Generate all necessary Terraform files for a Google Cloud deployment."""
        template_dir = Path(__file__).parent.parent / "terraform_templates"
        env = Environment(loader=FileSystemLoader(template_dir))

        # Generate main.tf, variables.tf, outputs.tf, and startup-script.sh
        self._generate_azure_main_files(deployment_dir, env, deployment)

        # Generate terraform.tfvars
        self._generate_tfvars(deployment_dir, env, deployment)

        # Generate config-template.toml
        self._generate_config_template(deployment_dir, env, deployment)

    def _generate_azure_main_files(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate main GCP Terraform files from templates."""
        main_template = env.get_template("azure_main.tf.j2")
        variables_template = env.get_template("azure_variables.tf.j2")
        outputs_template = env.get_template("azure_outputs.tf.j2")
        startup_script_template = env.get_template("azure_startup-script.sh.j2")

        with open(deployment_dir / "main.tf", "w") as f:
            f.write(main_template.render(package=deployment.package))
        with open(deployment_dir / "variables.tf", "w") as f:
            f.write(variables_template.render(package=deployment.package))
        with open(deployment_dir / "outputs.tf", "w") as f:
            f.write(outputs_template.render(package=deployment.package))
        with open(deployment_dir / "startup-script.sh", "w") as f:
            f.write(startup_script_template.render(
                package=deployment.package,
                komodo_provider_ip=get_settings().KOMODO_PROVIDER_IP,
                komodo_passkey=get_settings().KOMODO_PASSKEY
            ))

    def _generate_tfvars(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the terraform.tfvars file for GCP."""
        template = env.get_template("azure_terraform.tfvars.j2")
        tfvars_content = template.render(
            **self._get_terraform_vars(deployment)
        )
        with open(deployment_dir / "terraform.tfvars", "w") as f:
            f.write(tfvars_content)

    def _get_terraform_vars(self, deployment: DeploymentModel) -> dict:
        """Helper to construct a dictionary of Terraform variables."""
        client_name_lower = normalize_package_name_for_hostname(deployment.client_name)

        ssh_public_key = ""
        use_user_ssh_key = False
        settings = get_settings()
        ssh_strategy = settings.get_ssh_key_strategy(deployment.cloud_provider)

        # First priority: Use user's SSH key if provided
        if deployment.user_ssh_key:
            ssh_public_key = deployment.user_ssh_key.strip()
            use_user_ssh_key = True
            print("Using user-provided SSH key")
        else:
            # Strategy-based SSH key handling
            if ssh_strategy == "ENV_KEY_PER_DEPLOYMENT":
                # Use provider-specific SSH key file (Azure doesn't have SSH key resources)
                try:
                    ssh_public_key = settings.get_provider_ssh_key("Azure")
                    use_user_ssh_key = False  # Flag indicates source: False = provider key, True = user key
                    print(f"No user SSH key provided, using Azure-specific SSH key")
                except ValueError as e:
                    raise ValueError(f"Azure SSH key configuration error: {e}")
            else:  # SHARED_KEY_REFERENCE
                # This shouldn't happen for Azure, but handle gracefully
                print(f"No user SSH key provided, using SHARED_KEY_REFERENCE strategy")
                use_user_ssh_key = False

        vars_dict = {
            "azure_subscription_id": get_settings().AZURE_SUBSCRIPTION_ID,
            "azure_client_id": get_settings().AZURE_CLIENT_ID,
            "azure_client_secret": get_settings().AZURE_CLIENT_SECRET,
            "azure_tenant_id": get_settings().AZURE_TENANT_ID,
            "azure_location": deployment.region,
            "instance_name": f"{normalize_package_name_for_hostname(deployment.package)}-{client_name_lower}",
            "vm_size": deployment.instance_type,
            "ssh_username": "ubuntu",
            "ssh_public_key": ssh_public_key,
            "use_user_ssh_key": use_user_ssh_key,
            "shared_ssh_key": settings.get_provider_ssh_key("Azure") if ssh_strategy == "ENV_KEY_PER_DEPLOYMENT" and not deployment.user_ssh_key else "",
            "instance_tags": [normalize_package_name_for_hostname(deployment.package), client_name_lower],
            "firewall_name": f"allow-{normalize_package_name_for_hostname(deployment.package)}-{client_name_lower}",
            "client_name": deployment.client_name,
            "client_id": deployment.client_id,
            "package": deployment.package,
            "komodo_provider_endpoint": get_settings().KOMODO_PROVIDER_ENDPOINT,
            "komodo_api_key": get_settings().KOMODO_API_KEY,
            "komodo_api_secret": get_settings().KOMODO_API_SECRET,
            "github_token": get_settings().GITHUB_TOKEN,
            "domain": deployment.domain,
            "admin_email": deployment.admin_email,
            "admin_username": deployment.admin_username,
            "admin_password": deployment.admin_password,
            "admin_subdomain": deployment.admin_subdomain,
            "github_repo": deployment.github_repo,
            "traefik_subdomain": deployment.traefik_subdomain,
            "middleware_manager_subdomain": deployment.middleware_manager_subdomain,
            "nlweb_subdomain": deployment.nlweb_subdomain,
            "logs_subdomain": deployment.logs_subdomain,
            "premium_package": (deployment.package == "Premium"),
        }
        if deployment.package == "Premium":
            vars_dict.update({
                "crowdsec_enrollment_key": deployment.crowdsec_enrollment_key,
                "static_page_domain": deployment.static_page_domain,
                "oauth_client_id": deployment.oauth_client_id,
                "oauth_client_secret": deployment.oauth_client_secret,
                "openai_api_key": deployment.openai_api_key,
                "komodo_host_ip": deployment.komodo_host_ip,
            })
        return vars_dict

    def _generate_config_template(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the config-template.toml file."""
        template = env.get_template("azure_config-template.toml.j2")
        content = template.render(
            client_name=deployment.client_name,
            client_name_lower=normalize_package_name_for_hostname(deployment.client_name),
            domain=deployment.domain,
            admin_email=deployment.admin_email,
            admin_username=deployment.admin_username,
            admin_password=deployment.admin_password,
            admin_subdomain=deployment.admin_subdomain,
            github_repo=deployment.github_repo,
            traefik_subdomain=deployment.traefik_subdomain,
            middleware_manager_subdomain=deployment.middleware_manager_subdomain,
            nlweb_subdomain=deployment.nlweb_subdomain,
            logs_subdomain=deployment.logs_subdomain,
            setup_token=deployment.setup_token,
            package=deployment.package,
            crowdsec_enrollment_key=deployment.crowdsec_enrollment_key,
            static_page_subdomain=deployment.static_page_subdomain,
            maxmind_license_key=deployment.maxmind_license_key,
            oauth_client_id=deployment.oauth_client_id,
            oauth_client_secret=deployment.oauth_client_secret,
            openai_api_key=deployment.openai_api_key,
            komodo_host_ip=deployment.komodo_host_ip,
            komodo_passkey=deployment.komodo_passkey,
            support_level=deployment.support_level,
        )
        with open(deployment_dir / "config-template.toml", "w") as f:
            f.write(content)
