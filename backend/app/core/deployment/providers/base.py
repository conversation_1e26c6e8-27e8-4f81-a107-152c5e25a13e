
from abc import ABC, abstractmethod
from pathlib import Path
from app.models.deployment import Deployment as DeploymentModel
from jinja2 import Environment

class ProviderStrategy(ABC):
    """Abstract base class for cloud provider strategies."""

    @abstractmethod
    def generate_terraform_files(self, deployment_dir: Path, deployment: DeploymentModel) -> None:
        """Generate all necessary Terraform files for the deployment."""
        pass

    @abstractmethod
    def _generate_tfvars(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the terraform.tfvars file."""
        pass
