# Terraform configuration for Linode
terraform {
  required_providers {
    linode = {
      source  = "linode/linode"
      version = "~> 2.0"
    }
    komodo-provider = {
      source = "registry.example.com/mattercoder/komodo-provider"
      version = ">= 1.0.0"
    }
  }
}

# Configure the Linode Provider
provider "linode" {
  token = var.linode_token
}

# Conditionally create SSH key resource if user provided one
resource "linode_sshkey" "default" {
  count   = var.use_user_ssh_key ? 1 : 0
  label   = "terraform-key-${var.client_name_lower}"
  ssh_key = var.ssh_public_key
}

# Reference existing shared SSH key if user didn't provide one
data "linode_sshkey" "shared" {
  count = var.use_user_ssh_key ? 0 : 1
  label = "terraform-key"
}

# Create a Linode instance
resource "linode_instance" "main" {
  label       = var.instance_name
  image       = var.image
  type        = var.type
  region      = var.region
  # Use appropriate SSH key based on strategy
  authorized_keys = var.use_user_ssh_key ? [linode_sshkey.default[0].ssh_key] : [data.linode_sshkey.shared[0].ssh_key]
  root_pass   = var.root_password
  private_ip  = true

  metadata {
    user_data = base64encode(file("${path.module}/startup-script.sh"))
  }

  tags = [var.client_name_lower]
}

# Linode Firewall (requires enabling firewall service)
resource "linode_firewall" "main" {
  count = var.create_firewall ? 1 : 0
  label = "firewall-${var.client_name_lower}"
  
  inbound_policy  = "DROP"
  outbound_policy = "ACCEPT"

  inbound {
    label    = "Allow_SSH"
    action   = "ACCEPT"
    protocol = "TCP"
    ports    = "22"
    ipv4     = var.allowed_source_ips
  }

  inbound {
    label    = "Allow_HTTP"
    action   = "ACCEPT"
    protocol = "TCP"
    ports    = "80"
    ipv4     = var.allowed_source_ips
  }

  inbound {
    label    = "Allow_HTTPS"
    action   = "ACCEPT"
    protocol = "TCP"
    ports    = "443"
    ipv4     = var.allowed_source_ips
  }

  inbound {
    label    = "Allow_Komodo_API"
    action   = "ACCEPT"
    protocol = "TCP"
    ports    = "8120"
    ipv4     = var.allowed_source_ips
  }

  inbound {
    label    = "Allow_Komodo_UI"
    action   = "ACCEPT"
    protocol = "TCP"
    ports    = "9120"
    ipv4     = var.allowed_source_ips
  }

  inbound {
    label    = "Allow_Newt"
    action   = "ACCEPT"
    protocol = "UDP"
    ports    = "51820"
    ipv4     = var.allowed_source_ips
  }

  outbound {
    label    = "Allow_all_outbound"
    action   = "ACCEPT"
    protocol = "TCP"
    ports    = "1-65535"
    ipv4     = ["0.0.0.0/0"]
  }

  linodes = [linode_instance.main.id]
}

# Custom User Provider
provider "komodo-provider" {
  endpoint     = var.komodo_provider_endpoint
  api_key      = var.komodo_api_key
  api_secret   = var.komodo_api_secret
  github_token = var.github_token
  github_orgname = "ManidaeCloud"  
}

# Custom provider resource with templated configuration
resource "komodo-provider_user" "client_syncresources" {
  depends_on = [linode_instance.main]
  id           = var.client_id
  name         = var.client_name
  generate_ssh_keys = true
  file_contents = templatefile("${path.module}/config-template.toml", {
    client_name_lower        = lower(var.client_name)
    client_name              = var.client_name
    domain                   = var.domain
    admin_email              = var.admin_email
    admin_username           = var.admin_username
    admin_password           = var.admin_password
    admin_subdomain          = var.admin_subdomain
    postgres_user            = var.postgres_user
    postgres_password        = var.postgres_password
    postgres_host            = var.postgres_host
    github_repo              = var.github_repo
    {% if package in ["Pangolin+", "Pangolin+AI"] %}
    crowdsec_enrollment_key = var.crowdsec_enrollment_key
    static_page_subdomain   = var.static_page_subdomain
    maxmind_license_key     = var.maxmind_license_key
    {% endif %}
    {% if package == "Pangolin+AI" %}
    oauth_client_id         = var.oauth_client_id
    oauth_client_secret     = var.oauth_client_secret
    komodo_passkey          = var.komodo_passkey
    openai_api_key          = var.openai_api_key
    {% endif %}
  })
  server_ip = linode_instance.main.ip_address
}

# Output the server's IP address
output "server_ip" {
  value = linode_instance.main.ip_address
}
