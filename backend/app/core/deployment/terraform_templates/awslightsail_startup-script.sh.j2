{% if package in ["Coolify", "Coolify+"] %}
# Coolify-specific setup
echo "Setting up Coolify directories and configuration at $(date)"

# Create the base directories for Coolify under /data/coolify
mkdir -p /data/coolify/{source,ssh,applications,databases,backups,services,proxy,webhooks-during-maintenance}
mkdir -p /data/coolify/ssh/{keys,mux}
mkdir -p /data/coolify/proxy/dynamic

# Generate an SSH key for Coolify to manage your server
ssh-keygen -f /data/coolify/ssh/keys/<EMAIL> -t ed25519 -N '' -C root@coolify

# Add the public key to authorized_keys
cat /data/coolify/ssh/keys/<EMAIL> >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys

# Set the correct permissions for the Coolify files and directories
chown -R 9999:root /data/coolify
chmod -R 700 /data/coolify

# Ensure the Docker network is created
docker network create --attachable coolify

echo "Coolify setup completed at $(date)"
{% endif %}
bash -c 'set -e; exec > >(tee /var/log/user-data.log) 2>&1; echo "Starting user data script execution at $(date)"; apt-get update; apt-get install -y ca-certificates curl gnupg lsb-release git postgresql-client tesseract-ocr libtesseract-dev; mkdir -p /etc/apt/keyrings; curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg; echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null; apt-get update; apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin; usermod -aG docker root; curl -L "https://github.com/docker/compose/releases/download/v2.20.3/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose; chmod +x /usr/local/bin/docker-compose; mkdir -p /etc/komodo/stacks; apt-get install -y python3 python3-pip; pip3 install requests toml pyyaml; echo "Starting komodo periphery at $(date)"; curl -sSL https://raw.githubusercontent.com/moghtech/komodo/main/scripts/setup-periphery.py | sudo python3; sed -i "s/allowed_ips = \[\]/allowed_ips = [\"{{ komodo_provider_ip }}\"]/" /etc/komodo/periphery.config.toml; sed -i "s/passkeys = \[\]/passkeys = [\"{{ komodo_passkey }}\"]/" /etc/komodo/periphery.config.toml; systemctl restart periphery; ufw --force enable; ufw allow OpenSSH; ufw allow 80/tcp; ufw allow 443/tcp; ufw allow 8120/tcp; ufw allow 9120/tcp; ufw allow 51820/udp; ufw default deny incoming; ufw default allow outgoing; ufw status verbose; systemctl enable periphery; echo "User data script finished at $(date)"'