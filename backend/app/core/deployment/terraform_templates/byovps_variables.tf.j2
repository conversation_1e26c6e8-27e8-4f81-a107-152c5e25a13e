variable "komodo_provider_endpoint" { type = string }
variable "komodo_api_key" { type = string }
variable "komodo_api_secret" { type = string }
variable "github_token" { type = string }

variable "client_id" { type = string }
variable "client_name" { type = string }

variable "domain" { type = string }
variable "admin_email" { type = string }
variable "admin_username" { type = string }
variable "admin_password" { type = string }
variable "admin_subdomain" { type = string }
variable "postgres_user" { type = string }
variable "postgres_password" { type = string }
variable "postgres_host" { type = string }
variable "github_repo" { type = string }

variable "server_ip" { type = string }

# Premium-only variables (set safe defaults so Terraform won't prompt for Pangolin)
variable "crowdsec_enrollment_key" {
  type    = string
  default = ""
}

variable "static_page_domain" {
  type    = string
  default = ""
}

variable "oauth_client_id" {
  type    = string
  default = ""
}

variable "oauth_client_secret" {
  type    = string
  default = ""
}

variable "komodo_host_ip" {
  type    = string
  default = ""
}

variable "openai_api_key" {
  type    = string
  default = ""
}

