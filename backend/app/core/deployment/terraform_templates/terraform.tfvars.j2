# Client Information
client_name = "{{ client_name }}"
client_name_lower = "{{ client_name | lower | replace('_', '-') }}"
client_id = "{{ client_id }}"
package = "{{ package }}"

{% if cloud_provider == "Hetzner" %}
# Hetzner Configuration
hcloud_token = "{{ hcloud_token }}"
location = "{{ region }}"
server_type = "{{ instance_type }}"
os_type = "ubuntu-22.04"
instance_name = "{{ client_name | lower | replace('_', '-') }}-instance"
create_firewall = true
allowed_source_ips = ["0.0.0.0/0"]
{% endif %}

{% if cloud_provider == "Google Cloud" %}
# GCP Configuration
gcp_project_id = "{{ gcp_project_id }}"
gcp_region = "{{ region }}"
gcp_zone = "{{ zone }}"
instance_name = "{{ client_name | lower | replace('_', '-') }}-instance"
machine_type = "{{ instance_type }}"
firewall_name = "{{ client_name | lower | replace('_', '-') }}-firewall"
firewall_source_ranges = ["0.0.0.0/0"]
allowed_ports = ["22", "443", "8120", "9120"]
{% endif %}

# SSH Configuration
ssh_public_key = "{{ ssh_public_key }}"
{% if cloud_provider == "Google Cloud" %}
ssh_username = "{{ ssh_username or 'ubuntu' }}"
{% endif %}

# Komodo Provider Configuration
komodo_provider_endpoint = "{{ komodo_provider_endpoint }}"
komodo_api_key = "{{ komodo_api_key }}"
komodo_api_secret = "{{ komodo_api_secret }}"
github_token = "{{ github_token }}"

# Application Configuration
domain = "{{ domain }}"
admin_email = "{{ admin_email }}"
admin_username = "{{ admin_username }}"
admin_password = "{{ admin_password }}"
admin_subdomain = "{{ admin_subdomain }}"
postgres_user = "{{ postgres_user }}"
postgres_password = "{{ postgres_password }}"
postgres_host = "{{ postgres_host }}"
github_repo = "{{ github_repo }}"

{% if package in ["Pangolin+", "Pangolin+AI"] %}
# Pangolin+ Package Configuration
crowdsec_enrollment_key = "{{ crowdsec_enrollment_key or '' }}"
static_page_subdomain   = "{{ static_page_subdomain or '' }}"
maxmind_license_key     = "{{ maxmind_license_key or '' }}"
{% if not cloud_provider == "Hetzner" %}
# Only define allowed_source_ips here if not already defined above
allowed_source_ips = ["0.0.0.0/0"]
{% endif %}
{% endif %}

{% if package == "Pangolin+AI" %}
# Pangolin+AI Additional Configuration
{% if oauth_client_id %}
oauth_client_id         = "{{ oauth_client_id }}"
{% endif %}
{% if oauth_client_secret %}
oauth_client_secret     = "{{ oauth_client_secret }}"
{% endif %}
komodo_host_ip          = "{{ komodo_host_ip or '' }}"
komodo_passkey          = "{{ komodo_passkey or '' }}"
openai_api_key          = "{{ openai_api_key or '' }}"
{% endif %}

# Network Configuration
allowed_source_ips = ["0.0.0.0/0"]
