output "instance_ip" {
  description = "Public IP address of the Linode instance"
  value       = tolist(linode_instance.main.ipv4)[0]
}

output "instance_name" {
  description = "Name of the Linode instance"
  value       = linode_instance.main.label
}

output "instance_id" {
  description = "ID of the Linode instance"
  value       = linode_instance.main.id
}

output "instance_status" {
  description = "Status of the Linode instance"
  value       = linode_instance.main.status
}

output "instance_region" {
  description = "Region of the Linode instance"
  value       = linode_instance.main.region
}

output "client_id" {
  description = "Client ID"
  value       = "{{ client_id }}"
}

output "client_name" {
  description = "Client name"
  value       = "{{ client_name }}"
}

output "domain" {
  description = "Application domain"
  value       = var.domain
}

output "firewall_id" {
  description = "ID of the Linode firewall"
  value       = var.create_firewall ? linode_firewall.main[0].id : null
}

output "ssh_key_id" {
  description = "ID of the SSH key"
  value       = var.use_user_ssh_key ? linode_sshkey.default[0].id : data.linode_sshkey.shared[0].id
}
