# Azure Provider Configuration
variable "azure_subscription_id" {
  description = "The Azure subscription ID"
  type        = string
  default     = ""
}

variable "azure_client_id" {
  description = "The Azure client ID"
  type        = string
  default     = ""
  sensitive   = true
}

variable "azure_client_secret" {
  description = "The Azure client secret"
  type        = string
  default     = ""
  sensitive   = true
}

variable "azure_tenant_id" {
  description = "The Azure tenant ID"
  type        = string
  default     = ""
}

variable "azure_location" {
  description = "The Azure region"
  type        = string
  default     = "East US"
}

# Instance Configuration
variable "instance_name" {
  description = "Name of the Azure virtual machine"
  type        = string
  default     = "azure-client-instance"
}

variable "vm_size" {
  description = "Size of the Azure virtual machine"
  type        = string
  default     = "Standard_B2s"
}

# SSH Configuration
variable "ssh_public_key" {
  description = "SSH public key for instance access (only used when use_user_ssh_key is true)"
  type        = string
  default     = ""
  sensitive   = true
}

variable "use_user_ssh_key" {
  description = "Whether to use user-provided SSH key (true) or reference shared SSH key (false)"
  type        = bool
  default     = false
}

variable "shared_ssh_key" {
  description = "Shared SSH public key content (used when use_user_ssh_key is false)"
  type        = string
  default     = ""
  sensitive   = true
}

variable "ssh_username" {
  description = "Username for SSH access"
  type        = string
  default     = "adminuser"
}

# Client Configuration
variable "client_id" {
  description = "Client ID for the deployment"
  type        = string
  default     = "1"
}

variable "client_name" {
  description = "Client name for the deployment"
  type        = string
  default     = "Client1"
}

variable "domain" {
  description = "Domain for the application"
  type        = string
  default     = "example.com"
}

variable "admin_email" {
  description = "Admin email address"
  type        = string
  default     = "<EMAIL>"
}

variable "admin_username" {
  description = "Admin username"
  type        = string
  default     = "<EMAIL>"
}

variable "admin_password" {
  description = "Admin password"
  type        = string
  default     = ""
  sensitive   = true
}

variable "admin_subdomain" {
  description = "Admin subdomain"
  type        = string
  default     = "admin"
}

# Database Configuration
variable "postgres_user" {
  description = "PostgreSQL username"
  type        = string
  default     = "admin"
}

variable "postgres_password" {
  description = "PostgreSQL password"
  type        = string
  default     = ""
  sensitive   = true
}

variable "postgres_host" {
  description = "PostgreSQL host"
  type        = string
  default     = "postgres"
}


variable "package" {
  description = "Package type (Pangolin, Pangolin+, Pangolin+AI)"
  type        = string
}

# Custom Provider Configuration
variable "komodo_provider_endpoint" {
  description = "Custom provider endpoint"
  type        = string
  default     = "http://localhost:9120"
}

variable "komodo_api_key" {
  description = "API key for komodo provider"
  type        = string
  default     = ""
}

variable "komodo_api_secret" {
  description = "API secret for komodo provider"
  type        = string
  sensitive   = true
  default     = ""
}

variable "github_token" {
  description = "GitHub token for repository access"
  type        = string
  default     = ""
  sensitive   = true
}

# Repository Configuration
variable "github_repo" {
  description = "GitHub repository for application code"
  type        = string
  default     = "username/repo"
}

{% if package in ["Pangolin+", "Pangolin+AI"] %}
variable "crowdsec_enrollment_key" {
  description = "CrowdSec Enrollment key"
  type        = string
  sensitive   = true
}

variable "static_page_subdomain" {
  description = "Static page subdomain"
  type        = string
}

variable "maxmind_license_key" {
  description = "MaxMind license key for GeoIP"
  type        = string
  sensitive   = true
}
{% endif %}

{% if package == "Pangolin+AI" %}
variable "oauth_client_id" {
  description = "OAuth client ID"
  type        = string
  default     = ""
  sensitive   = true
}

variable "oauth_client_secret" {
  description = "OAuth client secret"
  type        = string
  default     = ""
  sensitive   = true
}

variable "komodo_host_ip" {
  description = "Komodo host IP for AI features"
  type        = string
}

variable "komodo_passkey" {
  description = "Komodo passkey for authentication"
  type        = string
  sensitive   = true
}

variable "openai_api_key" {
  description = "OpenAI API key"
  type        = string
  sensitive   = true
}
{% endif %}