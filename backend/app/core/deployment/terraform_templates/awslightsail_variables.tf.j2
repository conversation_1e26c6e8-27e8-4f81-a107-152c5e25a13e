# AWS Provider related variables
variable "aws_region" {
  description = "AWS region for Lightsail deployment (e.g., us-east-1)"
  type        = string
  default     = "us-east-1" # Example default for AWS region
}

variable "aws_availability_zone" {
  description = "AWS Lightsail Availability Zone (e.g., us-east-1a). Lightsail instances require an AZ."
  type        = string
  # IMPORTANT: You must pick an AZ within your chosen aws_region.
  # For example, if aws_region is "us-east-1", this could be "us-east-1a" or "us-east-1b", etc.
  # You might want to remove the default and set it explicitly in your .tfvars file.
  default     = "us-east-1a"
}

variable "aws_blueprint_id" {
  description = "Lightsail blueprint ID (image slug, e.g., 'ubuntu_22_04'). Use 'aws lightsail get-blueprints' to find available ones."
  type        = string
  default     = "ubuntu_22_04" # Example default for Lightsail Ubuntu 22.04
}

variable "aws_bundle_id" {
  description = "Lightsail bundle ID (instance size, e.g., 'nano_3_0' or 'micro_3_0'). Use 'aws lightsail get-bundles' to find available ones."
  type        = string
  default     = "micro_3_0" # Example default for a small Lightsail instance
}

# variables.tf
variable "aws_access_key" {
  description = "AWS Access Key ID"
  type        = string
  sensitive   = true # Mark as sensitive to hide in logs
}

variable "aws_secret_key" {
  description = "AWS Secret Access Key"
  type        = string
  sensitive   = true # Mark as sensitive to hide in logs
}

variable "ssh_public_key" {
  description = "SSH public key content for server access (required for AWS Lightsail due to regional restrictions)"
  type        = string
}

variable "use_user_ssh_key" {
  description = "Whether to use user-provided SSH key (true) or environment SSH key (false) - AWS always creates new key pair"
  type        = bool
  default     = false
}

variable "instance_name" {
  description = "Name of the AWS Lightsail instance"
  type        = string
}

variable "client_name" {
  description = "Client's name"
  type        = string
}

variable "client_name_lower" {
  description = "Lowercase version of the client's name"
  type        = string
}

variable "client_id" {
  description = "Unique identifier for the client"
  type        = string
}

variable "create_firewall" {
  description = "Whether to create firewall rules for the Lightsail instance"
  type        = bool
  default     = true
}

variable "allowed_source_ips" {
  description = "List of allowed source IPv4 CIDR ranges for SSH. For Lightsail, IPv6 is handled separately if needed."
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

# --- Komodo Provider and Application Configuration Variables (remain largely the same) ---

variable "domain" {
  description = "Application domain"
  type        = string
}

variable "admin_email" {
  description = "Admin user email"
  type        = string
}

variable "admin_username" {
  description = "Admin username"
  type        = string
}

variable "admin_password" {
  description = "Admin password"
  type        = string
  sensitive   = true
}

variable "admin_subdomain" {
  description = "Admin subdomain"
  type        = string
  default     = "admin"
}

variable "postgres_user" {
  description = "PostgreSQL username"
  type        = string
}

variable "postgres_password" {
  description = "PostgreSQL password"
  type        = string
  sensitive   = true
}

variable "postgres_host" {
  description = "PostgreSQL host address"
  type        = string
}

variable "github_repo" {
  description = "GitHub repository for client resources"
  type        = string
}

variable "komodo_provider_endpoint" {
  description = "Komodo provider API endpoint"
  type        = string
}

variable "komodo_api_key" {
  description = "Komodo API key"
  type        = string
  sensitive   = true
}

variable "komodo_api_secret" {
  description = "Komodo API secret"
  type        = string
  sensitive   = true
}

variable "github_token" {
  description = "GitHub token for authentication"
  type        = string
  sensitive   = true
}

{% if package in ["Pangolin+", "Pangolin+AI"] %}
variable "crowdsec_enrollment_key" {
  description = "CrowdSec Enrollment key"
  type        = string
  sensitive   = true
}

variable "static_page_subdomain" {
  description = "Static page subdomain"
  type        = string
}

variable "maxmind_license_key" {
  description = "MaxMind license key for GeoIP"
  type        = string
  sensitive   = true
}
{% endif %}

{% if package == "Pangolin+AI" %}
variable "oauth_client_id" {
  description = "OAuth client ID"
  type        = string
  default     = ""
  sensitive   = true
}

variable "oauth_client_secret" {
  description = "OAuth client secret"
  type        = string
  default     = ""
  sensitive   = true
}

variable "komodo_host_ip" {
  description = "Komodo host IP for AI features"
  type        = string
}

variable "komodo_passkey" {
  description = "Komodo passkey for authentication"
  type        = string
  sensitive   = true
}

variable "openai_api_key" {
  description = "OpenAI API key"
  type        = string
  sensitive   = true
}
{% endif %}