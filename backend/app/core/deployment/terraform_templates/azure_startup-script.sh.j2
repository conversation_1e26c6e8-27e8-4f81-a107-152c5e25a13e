#!/bin/bash
set -e

# Log all output to a file for debugging
exec > >(tee /var/log/user-data.log) 2>&1

echo "Starting user data script execution at $(date)"

# Update system packages
apt-get update

# Install required packages
apt-get install -y \
  ca-certificates \
  curl \
  gnupg \
  lsb-release \
  git \
  postgresql-client \
  tesseract-ocr \
  libtesseract-dev

# Add Docker's official GPG key
mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Set up the Docker repository
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
apt-get update
apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Create docker group and add default user
usermod -aG docker root

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.3/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create komodo directory structure
mkdir -p /etc/komodo/stacks

# Install Python and required packages
apt-get install -y python3 python3-pip python3-venv
python3 -m venv /etc/komodo/venv
/etc/komodo/venv/bin/pip install requests toml pyyaml

echo "Starting komodo periphery at $(date)"
# Run setup script for komodo periphery
curl -sSL https://raw.githubusercontent.com/moghtech/komodo/main/scripts/setup-periphery.py | sudo /etc/komodo/venv/bin/python

# Configure periphery with allowed IPs and passkeys
sed -i 's/allowed_ips = \[\]/allowed_ips = ["{{ komodo_provider_ip }}"]/' /etc/komodo/periphery.config.toml
sed -i 's/passkeys = \[\]/passkeys = ["{{ komodo_passkey }}"]/' /etc/komodo/periphery.config.toml

# Restart periphery service to apply configuration changes
systemctl restart periphery

# Enable periphery service
systemctl enable periphery

{% if package in ["Coolify", "Coolify+"] %}
# Coolify-specific setup
echo "Setting up Coolify directories and configuration at $(date)"

# Create the base directories for Coolify under /data/coolify
mkdir -p /data/coolify/{source,ssh,applications,databases,backups,services,proxy,webhooks-during-maintenance}
mkdir -p /data/coolify/ssh/{keys,mux}
mkdir -p /data/coolify/proxy/dynamic

# Generate an SSH key for Coolify to manage your server
ssh-keygen -f /data/coolify/ssh/keys/<EMAIL> -t ed25519 -N '' -C root@coolify

# Add the public key to authorized_keys
cat /data/coolify/ssh/keys/<EMAIL> >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys

# Set the correct permissions for the Coolify files and directories
chown -R 9999:root /data/coolify
chmod -R 700 /data/coolify

# Ensure the Docker network is created
docker network create --attachable coolify

echo "Coolify setup completed at $(date)"
{% endif %}
echo "User data script finished at $(date)"