# GCP Provider Configuration

gcp_project_id = "{{ gcp_project_id }}"
gcp_region     = "{{ gcp_region }}"
gcp_zone       = "{{ gcp_zone }}"
gcp_credentials_file = "{{ gcp_credentials_file }}"

# Instance Configuration
instance_name  = "{{ instance_name }}"
machine_type   = "{{ machine_type }}"
ssh_username   = "{{ ssh_username }}"
ssh_public_key = "{{ ssh_public_key }}"
use_user_ssh_key = {{ use_user_ssh_key | lower }}
shared_ssh_key = "{{ shared_ssh_key }}"
instance_tags  = {{ instance_tags | tojson }}
firewall_name  = "{{ firewall_name }}"

# Client Information
client_name = "{{ client_name }}"
client_id   = "{{ client_id }}"
package     = "{{ package }}"

# Komodo Provider Configuration
komodo_provider_endpoint = "{{ komodo_provider_endpoint }}"
komodo_api_key           = "{{ komodo_api_key }}"
komodo_api_secret        = "{{ komodo_api_secret }}"
github_token             = "{{ github_token }}"

# Application Configuration
domain          = "{{ domain }}"
admin_email     = "{{ admin_email }}"
admin_username  = "{{ admin_username }}"
admin_password  = "{{ admin_password }}"
admin_subdomain = "{{ admin_subdomain }}"
postgres_user     = "{{ postgres_user }}"
postgres_password = "{{ postgres_password }}"
postgres_host     = "{{ postgres_host }}"
github_repo       = "{{ github_repo }}"

{% if package in ["Pangolin+", "Pangolin+AI"] %}
# Pangolin+ Package Configuration
crowdsec_enrollment_key = "{{ crowdsec_enrollment_key or '' }}"
static_page_subdomain   = "{{ static_page_subdomain or '' }}"
maxmind_license_key     = "{{ maxmind_license_key or '' }}"
{% endif %}

{% if package == "Pangolin+AI" %}
# Pangolin+AI Additional Configuration
{% if oauth_client_id %}
oauth_client_id         = "{{ oauth_client_id }}"
{% endif %}
{% if oauth_client_secret %}
oauth_client_secret     = "{{ oauth_client_secret }}"
{% endif %}
komodo_host_ip          = "{{ komodo_host_ip or '' }}"
komodo_passkey          = "{{ komodo_passkey or '' }}"
openai_api_key          = "{{ openai_api_key or '' }}"
{% endif %}
