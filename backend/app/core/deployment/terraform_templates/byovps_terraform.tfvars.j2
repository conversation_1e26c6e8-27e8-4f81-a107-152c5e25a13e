client_name             = "{{ client_name }}"
client_id               = "{{ client_id }}"
domain                  = "{{ domain }}"
admin_email             = "{{ admin_email }}"
admin_username          = "{{ admin_username }}"
admin_password          = "{{ admin_password }}"
admin_subdomain         = "{{ admin_subdomain }}"
postgres_user           = "{{ postgres_user }}"
postgres_password       = "{{ postgres_password }}"
postgres_host           = "{{ postgres_host }}"

github_repo             = "{{ github_repo }}"

komodo_provider_endpoint = "{{ komodo_provider_endpoint }}"
komodo_api_key           = "{{ komodo_api_key }}"
komodo_api_secret        = "{{ komodo_api_secret }}"
github_token             = "{{ github_token }}"

server_ip               = "{{ server_ip }}"

{% if package in ["Pangolin+", "Pangolin+AI"] %}
# Pangolin+ Package Configuration
crowdsec_enrollment_key = "{{ crowdsec_enrollment_key or '' }}"
static_page_subdomain   = "{{ static_page_subdomain or '' }}"
maxmind_license_key     = "{{ maxmind_license_key or '' }}"
{% endif %}

{% if package == "Pangolin+AI" %}
# Pangolin+AI Additional Configuration
{% if oauth_client_id %}
oauth_client_id         = "{{ oauth_client_id }}"
{% endif %}
{% if oauth_client_secret %}
oauth_client_secret     = "{{ oauth_client_secret }}"
{% endif %}
komodo_host_ip          = "{{ komodo_host_ip or '' }}"
komodo_passkey          = "{{ komodo_passkey or '' }}"
openai_api_key          = "{{ openai_api_key or '' }}"
{% endif %}

