services:
  traefik:
    environment:
      - CROWDSEC_BOUNCER_API_KEY=${CROWDSEC_BOUNCER_API_KEY}
    labels:
      - "traefik.http.middlewares.crowdsec-bouncer.forwardauth.address=http://crowdsec-bouncer:8080/api/v1/forwardAuth"
      - "traefik.http.middlewares.crowdsec-bouncer.forwardauth.trustForwardHeader=true"
    depends_on:
      - crowdsec-bouncer

  crowdsec-bouncer:
    image: crowdsecurity/traefik-bouncer:latest
    container_name: crowdsec-bouncer
    environment:
      - CROWDSEC_BOUNCER_API_KEY=${CROWDSEC_BOUNCER_API_KEY}
      - CROWDSEC_AGENT_HOST=crowdsec:8080
    depends_on:
      - crowdsec
    networks:
      - coolify

  crowdsec:
    image: crowdsecurity/crowdsec:latest
    container_name: crowdsec
    environment:
      - COLLECTIONS=crowdsecurity/traefik crowdsecurity/http-cve
      - CROWDSEC_ENROLLMENT_KEY=${CROWDSEC_ENROLLMENT_KEY}
    volumes:
      - crowdsec-db:/var/lib/crowdsec/data/
      - crowdsec-config:/etc/crowdsec/
      - /var/log/traefik:/var/log/traefik:ro
    networks:
      - coolify

volumes:
  crowdsec-db:
  crowdsec-config:

networks:
  coolify:
    external: true
