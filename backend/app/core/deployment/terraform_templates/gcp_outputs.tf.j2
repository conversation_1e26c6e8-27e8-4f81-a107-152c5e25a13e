output "instance_ip" {
  description = "The public IP address of the GCP instance"
  value       = google_compute_instance.default.network_interface[0].access_config[0].nat_ip
}

output "instance_name" {
  description = "The name of the GCP instance"
  value       = google_compute_instance.default.name
}

output "instance_self_link" {
  description = "The self link of the GCP instance"
  value       = google_compute_instance.default.self_link
}

output "client_id" {
  description = "The client ID"
  value       = var.client_id
}

output "client_name" {
  description = "The client name"
  value       = var.client_name
}

output "domain" {
  description = "The domain for the application"
  value       = var.domain
}
