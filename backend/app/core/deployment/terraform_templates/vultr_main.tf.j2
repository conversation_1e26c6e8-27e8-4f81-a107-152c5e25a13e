terraform {
  required_providers {
    vultr = {
      source  = "vultr/vultr"
      version = ">= 2.26.0"
    }
    komodo-provider = {
      source = "registry.example.com/mattercoder/komodo-provider"
      version = ">= 1.0.0"
    }
  }
}

provider "vultr" {
  api_key = var.vultr_api_key
}

# Conditionally create SSH key resource if user provided one
resource "vultr_ssh_key" "default" {
  count   = var.use_user_ssh_key ? 1 : 0
  name    = "terraform-key-${var.client_name_lower}"
  ssh_key = var.ssh_public_key
}

# Reference existing shared SSH key if user didn't provide one
data "vultr_ssh_key" "shared" {
  count = var.use_user_ssh_key ? 0 : 1
  filter {
    name   = "name"
    values = ["terraform-key"]
  }
}

resource "vultr_instance" "main" {
  plan              = var.instance_type
  region            = var.region
  os_id             = var.os_id
  hostname          = var.instance_name
  label             = var.instance_name
  ssh_key_ids       = var.use_user_ssh_key ? [vultr_ssh_key.default[0].id] : [data.vultr_ssh_key.shared[0].id]
  firewall_group_id = var.create_firewall ? vultr_firewall_group.web[0].id : null

  tags = [var.client_name_lower]

  user_data = file("${path.module}/startup-script.sh")
}

resource "vultr_firewall_group" "web" {
  count       = var.create_firewall ? 1 : 0
  description = "firewall-${var.client_name_lower}"
}

resource "vultr_firewall_rule" "ssh" {
  count             = var.create_firewall ? 1 : 0
  firewall_group_id = vultr_firewall_group.web[0].id
  protocol          = "tcp"
  port              = "22"
  ip_type           = "v4"
  subnet            = "0.0.0.0"
  subnet_size       = 0
}

resource "vultr_firewall_rule" "http" {
  count             = var.create_firewall ? 1 : 0
  firewall_group_id = vultr_firewall_group.web[0].id
  protocol          = "tcp"
  port              = "80"
  ip_type           = "v4"
  subnet            = "0.0.0.0"
  subnet_size       = 0
}

resource "vultr_firewall_rule" "https" {
  count             = var.create_firewall ? 1 : 0
  firewall_group_id = vultr_firewall_group.web[0].id
  protocol          = "tcp"
  port              = "443"
  ip_type           = "v4"
  subnet            = "0.0.0.0"
  subnet_size       = 0
}

resource "vultr_firewall_rule" "komodo_core" {
  count             = var.create_firewall ? 1 : 0
  firewall_group_id = vultr_firewall_group.web[0].id
  protocol          = "tcp"
  port              = "8120"
  ip_type           = "v4"
  subnet            = "0.0.0.0"
  subnet_size       = 0
}

resource "vultr_firewall_rule" "komodo_periphery" {
  count             = var.create_firewall ? 1 : 0
  firewall_group_id = vultr_firewall_group.web[0].id
  protocol          = "tcp"
  port              = "9120"
  ip_type           = "v4"
  subnet            = "0.0.0.0"
  subnet_size       = 0
}

resource "vultr_firewall_rule" "wireguard_udp" {
  count             = var.create_firewall ? 1 : 0
  firewall_group_id = vultr_firewall_group.web[0].id
  protocol          = "udp"
  port              = "51820"
  ip_type           = "v4"
  subnet            = "0.0.0.0"
  subnet_size       = 0
}

provider "komodo-provider" {
  endpoint     = var.komodo_provider_endpoint
  api_key      = var.komodo_api_key
  api_secret   = var.komodo_api_secret
  github_token = var.github_token
  github_orgname = "ManidaeCloud"  
}

resource "komodo-provider_user" "client_syncresources" {
  depends_on = [vultr_instance.main]
  id           = var.client_id
  name         = var.client_name
  generate_ssh_keys = true
  file_contents = templatefile("${path.module}/config-template.toml", {
    client_name_lower        = lower(var.client_name)
    client_name              = var.client_name
    domain                   = var.domain
    admin_email              = var.admin_email
    admin_username           = var.admin_username
    admin_password           = var.admin_password
    admin_subdomain          = var.admin_subdomain
    postgres_user            = var.postgres_user
    postgres_password        = var.postgres_password
    postgres_host            = var.postgres_host
    github_repo              = var.github_repo
    {% if package in ["Pangolin+", "Pangolin+AI"] %}
    crowdsec_enrollment_key = var.crowdsec_enrollment_key
    static_page_subdomain   = var.static_page_subdomain
    maxmind_license_key     = var.maxmind_license_key
    {% endif %}
    {% if package == "Pangolin+AI" %}
    oauth_client_id         = var.oauth_client_id
    oauth_client_secret     = var.oauth_client_secret
    komodo_passkey          = var.komodo_passkey
    openai_api_key          = var.openai_api_key
    {% endif %}
  })
  server_ip = vultr_instance.main.main_ip
}
