import os
import logging
import shutil
from pathlib import Path
from typing import cast
from app.models.deployment import Deployment as DeploymentModel
from app.db.session import SessionLocal
from app.core.deployment.providers.base import ProviderStrategy
from app.core.deployment.providers.gcp import GoogleCloudStrategy
from app.core.deployment.providers.hetzner import HetznerStrategy
from app.core.deployment.providers.linode import LinodeStrategy
from app.core.deployment.providers.vultr import VultrStrategy
from app.core.deployment.providers.digitalocean import DigitalOceanStrategy
from app.core.deployment.providers.awslightsail import AwsLightsailStrategy
from app.core.deployment.providers.azure import AzureStrategy
from app.core.deployment.providers.byovps import BYOVPSStrategy
from app.core.deployment.terraform_runner import TerraformRunner
from app.services.billing_job import run_billing_for_user_deployment

class DeploymentManager:
    def __init__(self):
        self.provider_strategies = {
            "Google Cloud": GoogleCloudStrategy(),
            "Hetzner": HetznerStrategy(),
            "Linode": LinodeStrategy(),
            "Vultr": VultrStrategy(),
            "DigitalOcean": DigitalOceanStrategy(),
            "AWS Lightsail": AwsLightsailStrategy(),
            "Azure": AzureStrategy(),
            "BYOVPS": BYOVPSStrategy(),
        }

    def _get_provider_strategy(self, provider_name: str) -> ProviderStrategy:
        strategy = self.provider_strategies.get(provider_name)
        if not strategy:
            raise ValueError(f"Unsupported cloud provider: {provider_name}")
        return strategy

    def deploy(self, deployment_id: int, dry_run: bool = False) -> str:
        """Deploy the infrastructure using Terraform. On any failure, attempt to destroy created resources."""
        db = SessionLocal()
        deployment = None
        deployment_dir: Path | None = None
        try:
            deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
            if not deployment:
                raise ValueError(f"Deployment with ID {deployment_id} not found.")

            dry_run = dry_run or os.getenv('TERRAFORM_DRY_RUN', '').lower() in ('true', '1', 'yes')
            deployment_dir = self._create_deployment_directory(deployment)

            provider_key = cast(str, deployment.cloud_provider)
            if getattr(deployment, 'server_type', 'new') in ('vps', 'existing'):
                provider_key = 'BYOVPS'
            strategy = self._get_provider_strategy(provider_key)
            strategy.generate_terraform_files(deployment_dir, deployment)

            # Update status to PROVISIONING before starting terraform
            deployment.status = "PROVISIONING" # type: ignore
            db.add(deployment)
            db.commit()
            logging.info(f"Deployment {deployment.id} status updated to PROVISIONING")

            terraform_runner = TerraformRunner(deployment_dir)
            terraform_output = terraform_runner.init_and_apply(dry_run)

            assert deployment is not None
            if terraform_output and 'komodo_host_ip' in terraform_output:
                deployment.komodo_host_ip = terraform_output['komodo_host_ip']['value'] # type: ignore

            # Capture instance IP from terraform outputs
            if terraform_output:
                print(f"[DEBUG] Terraform outputs available: {terraform_output}")
                # Try different output names used by different providers
                instance_ip = None
                for ip_key in ['instance_ip', 'server_ip', 'instance_public_ip', 'server_ipv4']:
                    if ip_key in terraform_output:
                        instance_ip = terraform_output[ip_key]['value']
                        print(f"[DEBUG] Found IP address {instance_ip} from key {ip_key}")
                        break

                if instance_ip:
                    deployment.instance_ip = instance_ip # type: ignore
                    print(f"[DEBUG] Set deployment.instance_ip to {instance_ip}")
                else:
                    print(f"[DEBUG] No IP address found in terraform outputs")

            deployment.status = "ACTIVE" # type: ignore
            db.add(deployment)
            db.commit()
            logging.info(f"Deployment {deployment.id} infrastructure provisioned successfully")

            # Send activation email immediately when VPS is live (before DNS validation)
            # This ensures users know their VPS is running and billing has started
            try:
                from app.services.email_service import send_deployment_active_email
                u = deployment.user
                if u:
                    send_deployment_active_email(u.email, u.username, deployment)
                    logging.info(f"VPS activation email sent for deployment {deployment.id}")
            except Exception as e:
                logging.warning(f"Failed to send VPS activation email for deployment {deployment.id}: {e}")

            # Start DNS validation if domain is configured
            if deployment.domain:
                logging.info(f"Starting DNS validation for deployment {deployment.id}")
                deployment.status = "DNS_VALIDATION" # type: ignore
                db.add(deployment)
                db.commit()

                # Perform DNS validation
                dns_validation_result = self._validate_dns_configuration(deployment)

                if dns_validation_result['success']:
                    deployment.status = "READY" # type: ignore
                    logging.info(f"Deployment {deployment.id} DNS validation successful - deployment ready")
                else:
                    deployment.status = "DNS_FAILED" # type: ignore
                    logging.warning(f"Deployment {deployment.id} DNS validation failed: {dns_validation_result['message']}")

                db.add(deployment)
                db.commit()
            else:
                # No domain configured, deployment is ready
                deployment.status = "READY" # type: ignore
                db.add(deployment)
                db.commit()
                logging.info(f"Deployment {deployment.id} ready (no domain validation required)")

            # Trigger billing refresh for this deployment to ensure up-to-date calculations
            try:
                run_billing_for_user_deployment(deployment.user_id, deployment.id)
                logging.info(f"Billing refresh completed for deployment {deployment.id}")
            except Exception as billing_error:
                # Don't fail the deployment if billing refresh fails, just log it
                logging.warning(f"Billing refresh failed for deployment {deployment.id}: {billing_error}")

            return "Deployment successful"
        except Exception as e:
            logging.error(f"Deployment failed: {e}")
            # Best-effort cleanup: attempt destroy to avoid dangling resources
            try:
                if deployment_dir is not None:
                    TerraformRunner(deployment_dir).destroy()
            except Exception as destroy_err:
                logging.error(f"Automatic destroy after failure also failed: {destroy_err}")
            finally:
                if deployment is not None:
                    deployment.status = "FAILED" # type: ignore
                    deployment.failure_reason = str(e) # type: ignore
                    db.add(deployment)
                    db.commit()
            raise
        finally:
            db.close()

    def destroy(self, deployment_id: int):
        """Destroy the Terraform-managed infrastructure."""
        db = SessionLocal()
        try:
            deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
            if not deployment:
                raise ValueError(f"Deployment with ID {deployment_id} not found.")

            # Status should already be set to DESTROYING by the API endpoint
            logging.info(f"Deployment {deployment.id} destruction initiated")

            # Check if we're in dry run mode
            dry_run = os.getenv('TERRAFORM_DRY_RUN', '').lower() in ('true', '1', 'yes')
            if dry_run:
                print(f"[DRY RUN] Would destroy deployment {deployment_id}")
                deployment.status = "DESTROYED" # type: ignore
                db.add(deployment)
                db.commit()
                print(f"[DRY RUN] Deployment {deployment_id} marked as destroyed (no actual infrastructure changes)")
                return

            # --- Final Billing Logic ---
            # Perform a final billing run to ensure the last hour of usage is charged.
            from datetime import datetime, timedelta, timezone
            from decimal import Decimal
            from app.models.transaction import Transaction

            print(f"Performing final billing for deployment {deployment.id}...")
            try:
                # This logic is adapted from billing_job.py to be synchronous
                now_utc = datetime.now(timezone.utc)

                created_at_utc = deployment.created_at
                if created_at_utc.tzinfo is None:
                    created_at_utc = created_at_utc.replace(tzinfo=timezone.utc)

                if deployment.last_billed_at:
                    start_to_check_from = deployment.last_billed_at.replace(tzinfo=timezone.utc) if deployment.last_billed_at.tzinfo is None else deployment.last_billed_at
                    current_hour_to_check = start_to_check_from + timedelta(hours=1)
                else:
                    current_hour_to_check = created_at_utc.replace(minute=0, second=0, microsecond=0)

                last_successfully_billed_hour = None

                while current_hour_to_check < now_utc:
                    if created_at_utc > current_hour_to_check + timedelta(hours=1):
                        current_hour_to_check += timedelta(hours=1)
                        continue

                    hourly_cost_cents = deployment.cost or 0
                    charge_eur = Decimal(hourly_cost_cents) / Decimal(100)

                    if charge_eur > Decimal('0.00'):
                        user = deployment.user
                        if not user:
                            break

                        user.balance = (user.balance or Decimal("0")) - charge_eur
                        tx_description = f"Final usage charge for deployment #{deployment.id} (Hour: {current_hour_to_check.strftime('%Y-%m-%d %H:00')})"
                        tx = Transaction(user_id=user.id, amount=-charge_eur, type="billing", description=tx_description)
                        
                        db.add(user)
                        db.add(tx)
                        
                        last_successfully_billed_hour = current_hour_to_check
                    
                    current_hour_to_check += timedelta(hours=1)

                if last_successfully_billed_hour:
                    deployment.last_billed_at = last_successfully_billed_hour
                    db.add(deployment)
                
                db.commit()
                print(f"Final billing for deployment {deployment.id} complete.")

            except Exception as billing_err:
                db.rollback()
                logging.error(f"Could not perform final billing for deployment {deployment.id}: {billing_err}")
            # --- End of Final Billing ---

            deployment_dir = Path(f"backend/deployments/{deployment.id}")
            if not deployment_dir.exists():
                raise FileNotFoundError(f"Deployment directory not found: {deployment_dir}")

            terraform_runner = TerraformRunner(deployment_dir)
            terraform_runner.destroy()

            shutil.rmtree(deployment_dir)
            print(f"Deployment directory {deployment_dir} removed.")

            assert deployment is not None
            deployment.status = "DESTROYED" # type: ignore
            db.add(deployment)
            db.commit()
            
            # Trigger billing refresh for final charge calculation when deployment is destroyed
            try:
                run_billing_for_user_deployment(deployment.user_id, deployment.id)
                logging.info(f"Final billing refresh completed for destroyed deployment {deployment.id}")
            except Exception as billing_error:
                # Don't fail the destroy process if billing refresh fails, just log it
                logging.warning(f"Final billing refresh failed for destroyed deployment {deployment.id}: {billing_error}")
        finally:
            db.close()

    def cleanup_failed_deployment(self, deployment_id: int):
        """Clean up a failed deployment by removing deployment directory and updating status.
        This is a lighter cleanup method for deployments that failed during creation.
        """
        db = SessionLocal()
        try:
            deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
            if not deployment:
                # If deployment is not found in database, it may have already been cleaned up
                # Just try to clean up any remaining deployment directory
                logging.info(f"Deployment with ID {deployment_id} not found in database, checking for leftover files")

                deployment_dir = Path(f"backend/deployments/{deployment_id}")
                if deployment_dir.exists():
                    try:
                        # Attempt terraform destroy for any partially created resources
                        terraform_runner = TerraformRunner(deployment_dir)
                        terraform_runner.destroy()
                        logging.info(f"Successfully destroyed terraform resources for deployment {deployment_id}")
                    except Exception as terraform_err:
                        # Don't fail the cleanup if terraform destroy fails
                        logging.warning(f"Terraform destroy failed for deployment {deployment_id}: {terraform_err}")

                    try:
                        # Remove the deployment directory
                        shutil.rmtree(deployment_dir)
                        print(f"Deployment directory {deployment_dir} removed.")
                        logging.info(f"Removed leftover deployment directory for {deployment_id}")
                    except Exception as dir_err:
                        logging.warning(f"Failed to remove deployment directory {deployment_dir}: {dir_err}")
                else:
                    logging.info(f"No deployment directory found for {deployment_id}, cleanup already complete")

                return

            # Check if we're in dry run mode
            dry_run = os.getenv('TERRAFORM_DRY_RUN', '').lower() in ('true', '1', 'yes')
            if dry_run:
                print(f"[DRY RUN] Would clean up failed deployment {deployment_id}")
                logging.info(f"Failed deployment {deployment.id} cleanup completed (dry run)")
                return

            deployment_dir = Path(f"backend/deployments/{deployment.id}")

            # Try to clean up any terraform state if directory exists
            if deployment_dir.exists():
                try:
                    # Attempt terraform destroy for any partially created resources
                    terraform_runner = TerraformRunner(deployment_dir)
                    terraform_runner.destroy()
                    logging.info(f"Successfully destroyed terraform resources for failed deployment {deployment.id}")
                except Exception as terraform_err:
                    # Don't fail the cleanup if terraform destroy fails - this is expected for failed deployments
                    logging.warning(f"Terraform destroy failed for failed deployment {deployment.id}: {terraform_err}")

                try:
                    # Remove the deployment directory
                    shutil.rmtree(deployment_dir)
                    print(f"Deployment directory {deployment_dir} removed.")
                except Exception as dir_err:
                    logging.warning(f"Failed to remove deployment directory {deployment_dir}: {dir_err}")

            # Update deployment status to DESTROYED
            deployment.status = "DESTROYED" # type: ignore
            db.add(deployment)
            db.commit()

            logging.info(f"Failed deployment {deployment.id} cleanup completed")

        except Exception as e:
            logging.error(f"Failed to cleanup failed deployment {deployment_id}: {e}")
            raise
        finally:
            db.close()

    def _validate_dns_configuration(self, deployment) -> dict:
        """
        Validate that the deployment's domain points to the correct IP address.
        Returns dict with 'success' boolean and 'message' string.
        """
        try:
            from app.services.dns_ssl_monitor import dns_ssl_checker

            # Get the public IP address for this deployment
            # instance_ip: for new cloud instances
            # vps_ip_address: for BYOVPS deployments
            target_ip = deployment.instance_ip or deployment.vps_ip_address

            if not target_ip:
                return {
                    'success': False,
                    'message': 'No public IP address available for DNS validation (checked instance_ip, vps_ip_address)'
                }

            domains = dns_ssl_checker.get_deployment_domains(deployment)
            if not domains:
                return {
                    'success': True,
                    'message': 'No domains to validate'
                }

            failed_domains = []
            for domain in domains:
                dns_ok, dns_message = dns_ssl_checker.check_dns_resolution(domain, target_ip)
                if not dns_ok:
                    failed_domains.append(f"{domain}: {dns_message}")

            if failed_domains:
                return {
                    'success': False,
                    'message': f"DNS validation failed for: {'; '.join(failed_domains)}"
                }
            else:
                return {
                    'success': True,
                    'message': f"DNS validation successful for {len(domains)} domain(s)"
                }

        except Exception as e:
            logging.error(f"DNS validation error for deployment {deployment.id}: {e}")
            return {
                'success': False,
                'message': f"DNS validation error: {str(e)}"
            }

    def retry_dns_validation(self, deployment_id: int):
        """Retry DNS validation for a deployment with DNS_FAILED status"""
        db = SessionLocal()
        try:
            deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
            if not deployment:
                raise ValueError(f"Deployment with ID {deployment_id} not found.")

            if deployment.status != "DNS_FAILED":
                raise ValueError(f"Can only retry DNS validation for deployments with DNS_FAILED status. Current status: {deployment.status}")

            logging.info(f"Retrying DNS validation for deployment {deployment_id}")
            deployment.status = "DNS_VALIDATION"
            db.add(deployment)
            db.commit()

            # Perform DNS validation
            dns_validation_result = self._validate_dns_configuration(deployment)

            if dns_validation_result['success']:
                deployment.status = "READY"
                logging.info(f"Deployment {deployment_id} DNS validation retry successful")

                # Trigger billing refresh for this deployment - same as in initial deployment
                try:
                    from app.services.billing_job import run_billing_for_user_deployment
                    run_billing_for_user_deployment(deployment.user_id, deployment.id)
                    logging.info(f"Billing refresh completed for deployment {deployment.id}")
                except Exception as billing_error:
                    # Don't fail the deployment if billing refresh fails, just log it
                    logging.warning(f"Billing refresh failed for deployment {deployment.id}: {billing_error}")
            else:
                deployment.status = "DNS_FAILED"
                logging.warning(f"Deployment {deployment_id} DNS validation retry failed: {dns_validation_result['message']}")

            db.add(deployment)
            db.commit()

            return dns_validation_result

        except Exception as e:
            logging.error(f"DNS validation retry failed: {e}")
            # If retry fails, mark as failed
            deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
            if deployment:
                deployment.status = "DNS_FAILED"
                db.add(deployment)
                db.commit()
            raise
        finally:
            db.close()

    def restart(self, deployment_id: int):
        """Restart the deployment by executing the restart procedure in Komodo.
        This stops and starts the stack without destroying infrastructure.
        """
        db = SessionLocal()
        try:
            deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
            if not deployment:
                raise ValueError(f"Deployment with ID {deployment_id} not found.")

            if deployment.status not in ["ACTIVE", "READY"]:
                raise ValueError(f"Can only restart deployments with infrastructure ready. Current status: {deployment.status}")

            # Update status to indicate restart in progress
            deployment.status = "RESTARTING"
            db.add(deployment)
            db.commit()
            logging.info(f"Deployment {deployment.id} status updated to RESTARTING")

            # Get the deployment directory (should already exist from initial deployment)
            # Check both possible locations for the deployment directory
            deployment_dir = Path(f"backend/deployments/{deployment.id}")
            if not deployment_dir.exists():
                deployment_dir = Path(f"backend/backend/deployments/{deployment.id}")

            if not deployment_dir.exists():
                raise ValueError(f"Deployment directory not found at backend/deployments/{deployment.id} or backend/backend/deployments/{deployment.id}")

            # Execute restart procedure via terraform
            print(f"[DEBUG] About to create TerraformRunner for {deployment_dir}")
            terraform_runner = TerraformRunner(deployment_dir)
            print(f"[DEBUG] About to call terraform_runner.restart for {deployment.client_name}")
            terraform_runner.restart(deployment.client_name)
            print(f"[DEBUG] terraform_runner.restart completed successfully")

            # Update status back to ACTIVE
            print(f"[DEBUG] Setting deployment status back to ACTIVE")
            deployment.status = "ACTIVE"
            db.add(deployment)
            print(f"[DEBUG] About to commit database changes")
            db.commit()
            print(f"[DEBUG] Database commit successful")
            logging.info(f"Deployment {deployment.id} restart completed successfully")

        except Exception as e:
            logging.error(f"Deployment restart failed: {e}")
            # If restart fails, mark as failed
            deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
            if deployment:
                deployment.status = "FAILED"
                db.add(deployment)
                db.commit()
            raise
        finally:
            db.close()

    def _create_deployment_directory(self, deployment: DeploymentModel) -> Path:
        """Create a directory for the deployment files."""
        deployment_dir = Path(f"backend/deployments/{deployment.id}")
        if os.path.exists(deployment_dir):
            # Clean up any previously orphaned deployment directories
            shutil.rmtree(deployment_dir)
            print(f"Previously orphaned Deployment directory {deployment_dir} removed.")        
        deployment_dir.mkdir(parents=True, exist_ok=True)
        return deployment_dir