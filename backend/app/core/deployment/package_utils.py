def normalize_package_name(package: str) -> str:
    """
    Normalize package name for template compatibility.
    Keep the new package names as-is since templates have been updated.
    """
    return package

def normalize_package_name_for_hostname(package: str) -> str:
    """
    Normalize package name for use in hostnames and server names.
    Removes special characters that are not allowed in hostnames.
    """
    # Replace + with 'plus' and other special characters with hyphens
    normalized = package.replace('+', 'plus').replace(' ', '-').replace('_', '-')
    # Remove any other special characters that might cause issues
    import re
    normalized = re.sub(r'[^a-zA-Z0-9\-]', '', normalized)
    return normalized.lower()

def is_premium_package(package: str) -> bool:
    """
    Check if a package is an advanced package (Pangolin+, Pangolin+AI, or Coolify+).
    """
    return package in ["Pangolin+", "Pangolin+AI", "Coolify+"]

def is_ai_package(package: str) -> bool:
    """
    Check if a package includes AI features.
    """
    return package == "Pangolin+AI"