
import os
import logging
from pathlib import Path
from python_terraform import Terraform # type: ignore

# Set up logger
logger = logging.getLogger(__name__)

class TerraformRunner:
    """A class to encapsulate Terraform command execution."""

    def __init__(self, deployment_dir: Path):
        self.deployment_dir = deployment_dir
        self.terraform = Terraform(working_dir=str(deployment_dir))
        logger.info(f"TerraformRunner initialized with deployment_dir: {deployment_dir}")

    def init_and_apply(self, dry_run: bool = False):
        """Initialize and apply the Terraform configuration."""
        logger.info(f"Starting init_and_apply, dry_run={dry_run}")
        print(f"[TERRAFORM] Starting init_and_apply, dry_run={dry_run}")

        logger.info("Running terraform init...")
        print("[TERRAFORM] Running terraform init...")
        init_result = self.terraform.init()
        logger.info(f"Terraform init result: return_code={init_result[0]}")
        print(f"[TERRAFORM] Terraform init result: return_code={init_result[0]}")
        if init_result[1]:
            logger.info(f"Terraform init stdout: {init_result[1]}")
            print(f"[TERRAFORM] Terraform init stdout: {init_result[1]}")
        if init_result[2]:
            logger.warning(f"Terraform init stderr: {init_result[2]}")
            print(f"[TERRAFORM] Terraform init stderr: {init_result[2]}")

        if init_result[0] != 0:
            logger.error("Terraform init failed")
            print("[TERRAFORM] ERROR: Terraform init failed")
            raise Exception(f"Terraform init failed: {init_result[2]}")

        if dry_run:
            logger.info("DRY RUN: Mocking terraform apply...")
            print("[TERRAFORM] DRY RUN: Mocking terraform apply...")
            return

        logger.info("Running terraform apply...")
        print("[TERRAFORM] Running terraform apply...")
        apply_result = self.terraform.apply(skip_plan=True)
        logger.info(f"Terraform apply result: return_code={apply_result[0]}")
        print(f"[TERRAFORM] Terraform apply result: return_code={apply_result[0]}")
        if apply_result[1]:
            logger.info(f"Terraform apply stdout: {apply_result[1]}")
            print(f"[TERRAFORM] Terraform apply stdout: {apply_result[1]}")
        if apply_result[2]:
            logger.warning(f"Terraform apply stderr: {apply_result[2]}")
            print(f"[TERRAFORM] Terraform apply stderr: {apply_result[2]}")

        if apply_result[0] != 0:
            logger.error("Terraform apply failed")
            print("[TERRAFORM] ERROR: Terraform apply failed")
            raise Exception(f"Terraform apply failed: {apply_result[2]}")

        logger.info("Terraform init_and_apply completed successfully")
        print("[TERRAFORM] Terraform init_and_apply completed successfully")

        if not dry_run:
            outputs = self.terraform.output()
            logger.info(f"Terraform outputs: {outputs}")
            print(f"[TERRAFORM] Terraform outputs: {outputs}")
            return outputs
        return None

    def destroy(self):
        """Destroy the Terraform-managed infrastructure.
        Includes a fallback path that prunes komodo-provider state before destroy,
        to ensure compute instances are torn down even if Komodo API is unavailable.
        """
        logger.info("Starting terraform destroy...")
        print("[TERRAFORM] Starting terraform destroy...")

        # Ensure optional vars have defaults to prevent prompts
        self._ensure_optional_vars_have_defaults()

        # First attempt a plain destroy
        command = "terraform destroy -input=false -auto-approve"
        print(f"[TERRAFORM] Running command: cd {self.deployment_dir} && {command}")
        result = os.system(f"cd {self.deployment_dir} && {command}")
        print(f"[TERRAFORM] Terraform destroy result: exit_code={result}")

        if result != 0:
            logger.warning("Terraform destroy failed, retrying with komodo state pruned")
            print("[TERRAFORM] WARN: Destroy failed, retrying with komodo state pruned")
            # Prune komodo-related resources from state if they exist
            self._remove_komodo_from_state()
            # Retry destroy
            result = os.system(f"cd {self.deployment_dir} && {command}")
            print(f"[TERRAFORM] Terraform destroy result (retry): exit_code={result}")

        if result != 0:
            logger.error("Terraform destroy failed")
            print("[TERRAFORM] ERROR: Terraform destroy failed")
            raise Exception(f"Terraform destroy failed with exit code {result}")

        logger.info("Terraform destroy completed successfully")
        print("[TERRAFORM] Terraform destroy completed successfully")

    def restart(self, client_name: str):
        """Restart the deployment by calling the Komodo API directly to run the restart procedure.
        This bypasses terraform and directly executes the restart procedure in Komodo.
        """
        logger.info(f"Starting restart for {client_name} via direct Komodo API call...")
        print(f"[TERRAFORM] Starting restart for {client_name}...")

        try:
            # Get Komodo connection details from terraform.tfvars
            tfvars_file = self.deployment_dir / "terraform.tfvars"
            komodo_endpoint = None
            komodo_api_key = None
            komodo_api_secret = None

            if tfvars_file.exists():
                with open(tfvars_file, 'r') as f:
                    content = f.read()
                    # Extract Komodo connection details (check both possible field names)
                    import re
                    endpoint_match = re.search(r'komodo_(?:provider_)?endpoint\s*=\s*"([^"]+)"', content)
                    key_match = re.search(r'komodo_api_key\s*=\s*"([^"]+)"', content)
                    secret_match = re.search(r'komodo_api_secret\s*=\s*"([^"]+)"', content)

                    if endpoint_match:
                        komodo_endpoint = endpoint_match.group(1)
                    if key_match:
                        komodo_api_key = key_match.group(1)
                    if secret_match:
                        komodo_api_secret = secret_match.group(1)

            if not all([komodo_endpoint, komodo_api_key, komodo_api_secret]):
                raise Exception("Could not find Komodo connection details in terraform.tfvars")

            # Make direct API call to run the restart procedure
            import requests
            import json

            procedure_name = f"{client_name}_ProcedureRestart"
            payload = {
                "type": "RunProcedure",
                "params": {
                    "procedure": procedure_name
                }
            }

            headers = {
                "Content-Type": "application/json",
                "X-Api-Key": komodo_api_key,
                "X-Api-Secret": komodo_api_secret
            }

            api_url = f"{komodo_endpoint}/execute"
            print(f"[TERRAFORM] Calling Komodo API: {api_url}")
            print(f"[TERRAFORM] Running procedure: {procedure_name}")

            response = requests.post(api_url, json=payload, headers=headers, timeout=30)

            print(f"[TERRAFORM] API Response Status: {response.status_code}")
            print(f"[TERRAFORM] API Response Text: {response.text}")

            if response.status_code == 200:
                print(f"[TERRAFORM] Restart procedure executed successfully")
                logger.info(f"Restart procedure {procedure_name} executed successfully")
            else:
                raise Exception(f"Komodo API call failed: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"Restart failed: {e}")
            print(f"[TERRAFORM] ERROR: Restart failed: {e}")
            raise Exception(f"Restart failed: {e}")

        logger.info(f"Restart completed successfully for {client_name}")
        print(f"[TERRAFORM] Restart completed successfully for {client_name}")

    def _ensure_optional_vars_have_defaults(self):
        """Create an auto tfvars file with empty defaults for optional vars if they're not already set.
        This helps avoid interactive prompts during destroy for Pangolin/BYOVPS deployments that don't use Premium vars.
        """
        tfvars_path = self.deployment_dir / "terraform.tfvars"
        existing_keys = set()
        if tfvars_path.exists():
            try:
                content = tfvars_path.read_text()
                for line in content.splitlines():
                    if "=" in line and not line.strip().startswith("#"):
                        key = line.split("=")[0].strip()
                        if key:
                            existing_keys.add(key)
            except Exception:
                pass

        optional_keys = [
            "crowdsec_enrollment_key",
            "static_page_domain",
            "oauth_client_id",
            "oauth_client_secret",
            "komodo_host_ip",
            "openai_api_key",
        ]

        lines = []
        for key in optional_keys:
            if key not in existing_keys:
                lines.append(f"{key} = \"\"")
        if lines:
            auto_path = self.deployment_dir / "auto_defaults.auto.tfvars"
            auto_path.write_text("\n".join(lines) + "\n")
            print(f"[TERRAFORM] Wrote defaults to {auto_path} for missing optional vars: {', '.join(optional_keys)}")

    def _remove_komodo_from_state(self):
        """Best-effort removal of komodo resources from state to allow destroy to proceed.
        This avoids failures when the Komodo provider endpoints are unreachable.
        """
        try:
            # List state resources
            list_cmd = f"cd {self.deployment_dir} && terraform state list"
            stream = os.popen(list_cmd)
            resources = stream.read().splitlines()
            # Remove any resources with 'komodo-provider' in their address
            for addr in resources:
                if 'komodo-provider' in addr or 'komodo_provider' in addr:
                    rm_cmd = f"cd {self.deployment_dir} && terraform state rm {addr}"
                    print(f"[TERRAFORM] Removing from state: {addr}")
                    os.system(rm_cmd)
        except Exception as e:
            print(f"[TERRAFORM] WARN: Failed to prune komodo resources from state: {e}")


