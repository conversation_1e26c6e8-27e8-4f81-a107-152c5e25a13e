import csv
import os
from pathlib import Path
from typing import Dict, Optional

from app.db.session import SessionLocal
from app.models.instance_pricing import InstancePricing

class PricingService:
    def __init__(self):
        self.pricing_data = []
        self.load_pricing_data()

    def reload_pricing_data(self):
        """Reload pricing data from database (useful after CSV updates)"""
        print("Reloading pricing data from database...")
        self.load_pricing_data()
        print(f"Reloaded {len(self.pricing_data)} pricing records")

    def load_pricing_data(self):
        """Load pricing data from database, fallback to CSV if needed"""
        db = SessionLocal()
        try:
            # Try to load from database first
            db_pricing = db.query(InstancePricing).all()
            if db_pricing:
                # Convert DB objects to dictionaries for consistent access
                self.pricing_data = [{
                    'cloud_provider': item.cloud_provider,
                    'instance_type': item.instance_type,
                    'region': item.region,
                    'hourly_cost': str(item.cost_per_hour),
                    'package_multiplier_pangolin': str(item.package_multiplier_pangolin),
                    'package_multiplier_pangolin+': str(item.package_multiplier_pangolin_plus),
                    'package_multiplier_pangolin+AI': str(item.package_multiplier_pangolin_plus_ai),
                    'package_multiplier_coolify': str(item.package_multiplier_coolify),
                    'support_cost_level1': str(item.support_cost_level1),
                    'support_cost_level2': str(item.support_cost_level2),
                    'support_cost_level3': str(item.support_cost_level3),
                    'active': str(int(item.active)) if hasattr(item, 'active') else '1',
                    'cpu': str(item.cpu) if hasattr(item, 'cpu') and item.cpu is not None else '1',
                    'memory': str(item.memory) if hasattr(item, 'memory') and item.memory is not None else '1',
                    'country_code': str(item.country_code) if hasattr(item, 'country_code') and item.country_code is not None else ''
                } for item in db_pricing]
                print(f"Loaded {len(self.pricing_data)} pricing records from database")
                return

            # If no data in DB, load from CSV
            print("No pricing data found in database, loading from CSV...")
            self._load_from_csv()
        finally:
            db.close()

    def _load_from_csv(self):
        """Fallback method to load pricing data from CSV file"""
        # Try multiple possible locations for the pricing.csv file
        possible_paths = [
            # Path when run from backend directory
            Path(__file__).parent.parent.parent / "config" / "pricing.csv",
            # Path when run from project root
            Path(__file__).parent.parent.parent.parent / "config" / "pricing.csv",
            # Path for the new location (after you move it)
            Path(__file__).parent.parent.parent / "config" / "pricing.csv"
        ]

        # Try each path until we find one that exists
        csv_path = None
        for path in possible_paths:
            if os.path.exists(path):
                csv_path = path
                break

        if not csv_path:
            raise FileNotFoundError(f"Pricing CSV file not found in any of the expected locations: {possible_paths}")

        with open(csv_path, 'r') as file:
            reader = csv.DictReader(file)
            self.pricing_data = list(reader)
            print(f"Loaded {len(self.pricing_data)} pricing records from CSV at {csv_path}")

    def _get_support_cost(self, support_level: str) -> float:
        """Return the hourly support cost for the given level using pricing data defaults."""
        if not self.pricing_data:
            return 0.0
        try:
            level_num = str(support_level).split()[-1]
            key = f"support_cost_level{level_num}"
            # Use the first row as canonical support pricing source
            return float(self.pricing_data[0].get(key, 0.0))
        except Exception:
            return 0.0

    def _calculate_byovps_pricing(self, package: str, support_level: str) -> Dict:
        """Calculate pricing for BYOVPS using CSV data."""
        # Find BYOVPS entry in pricing data (only active entries)
        byovps_entry = None
        for entry in self.pricing_data:
            # Check if entry is active (default to active if not specified)
            is_active = entry.get('active', '1') == '1'
            if not is_active:
                continue

            if entry.get('cloud_provider') == 'BYOVPS':
                byovps_entry = entry
                break

        if not byovps_entry:
            # Fallback to old method if BYOVPS not found in CSV
            return self._support_only_result(support_level)

        # Get base costs from CSV
        base_hourly_cost = float(byovps_entry.get('hourly_cost', 0.0))
        support_cost = self._get_support_cost_from_entry(byovps_entry, support_level)

        # Apply package multiplier to base cost
        package_multiplier = self._get_package_multiplier(byovps_entry, package)
        adjusted_base_cost = base_hourly_cost * package_multiplier

        # Total cost = adjusted base cost + support cost
        hourly_cost = adjusted_base_cost + support_cost
        daily_cost = hourly_cost * 24
        monthly_cost = hourly_cost * 24 * 30

        return {
            "hourly_cost": round(hourly_cost, 4),
            "daily_cost": round(daily_cost, 2),
            "monthly_cost": round(monthly_cost, 2),
            "breakdown": {
                "instance_cost": 0.0,  # No instance cost for BYOVPS
                "storage_cost": 0.0,   # No storage cost for BYOVPS
                "network_cost": 0.0,   # No network cost for BYOVPS
                "support_cost": round(support_cost, 4),
                "byovps_management_fee": round(adjusted_base_cost, 4)
            }
        }

    def _get_support_cost_from_entry(self, entry: Dict, support_level: str) -> float:
        """Get support cost from a specific pricing entry."""
        try:
            level_num = str(support_level).split()[-1]
            key = f"support_cost_level{level_num}"
            return float(entry.get(key, 0.0))
        except Exception:
            return 0.0

    def _get_package_multiplier(self, entry: Dict, package: str) -> float:
        """Get package multiplier from a pricing entry."""
        package_name_mapping = {
            "pangolin": "package_multiplier_pangolin",
            "pangolin+": "package_multiplier_pangolin+",
            "pangolin+ai": "package_multiplier_pangolin+AI",
            "coolify": "package_multiplier_coolify",
            "coolify+": "package_multiplier_coolify+"
        }

        package_multiplier_key = package_name_mapping.get(package.lower(), "package_multiplier_pangolin")
        package_multiplier_value = entry.get(package_multiplier_key, 1.0)
        return float(package_multiplier_value) if package_multiplier_value is not None else 1.0

    def _support_only_result(self, support_level: str) -> Dict:
        from app.core.config import get_settings
        settings = get_settings()

        support_cost = self._get_support_cost(support_level)
        # Add BYOVPS daily fee (convert from daily to hourly)
        byovps_daily_fee = settings.BILLING_BYOVPS_DAILY_FEE
        byovps_hourly_fee = byovps_daily_fee / 24  # Convert daily fee to hourly
        hourly_cost = support_cost + byovps_hourly_fee
        daily_cost = hourly_cost * 24
        monthly_cost = hourly_cost * 24 * 30
        return {
            "hourly_cost": round(hourly_cost, 4),
            "daily_cost": round(daily_cost, 2),
            "monthly_cost": round(monthly_cost, 2),
            "breakdown": {
                "instance_cost": 0.0,
                "storage_cost": 0.0,
                "network_cost": 0.0,
                "support_cost": round(support_cost, 4),
                "byovps_management_fee": round(byovps_hourly_fee, 4)
            }
        }

    def calculate_pricing(self, cloud_provider: Optional[str], instance_type: Optional[str], region: Optional[str],
                         package: str, support_level: str, server_type: str = "new") -> Dict:
        """Calculate pricing based on configuration and server type."""
        print(f"Calculating pricing for: server_type={server_type}, provider={cloud_provider}, instance_type={instance_type}, region={region}, package={package}, support={support_level}")

        if server_type in ("vps", "existing"):
            # Use BYOVPS pricing from CSV
            return self._calculate_byovps_pricing(package, support_level)

        # Require provider details for 'new' server type
        if not cloud_provider or not instance_type or not region:
            print("Missing provider details for 'new' server pricing; returning zeros")
            return {
                "hourly_cost": 0.0,
                "daily_cost": 0.0,
                "monthly_cost": 0.0,
                "breakdown": {
                    "instance_cost": 0.0,
                    "storage_cost": 0.0,
                    "network_cost": 0.0,
                    "support_cost": 0.0
                }
            }

        # Find the matching pricing row (only active entries)
        pricing_row = None
        for row in self.pricing_data:
            # Check if entry is active (default to active if not specified)
            is_active = row.get('active', '1') == '1'
            if not is_active:
                continue

            if (row['cloud_provider'] == cloud_provider and
                row['instance_type'] == instance_type and
                row['region'] == region):
                pricing_row = row
                break

        if not pricing_row:
            print(f"No matching pricing data found for {cloud_provider}, {instance_type}, {region}")
            return {
                "hourly_cost": 0.0,
                "daily_cost": 0.0,
                "monthly_cost": 0.0,
                "breakdown": {
                    "instance_cost": 0.0,
                    "storage_cost": 0.0,
                    "network_cost": 0.0,
                    "support_cost": 0.0
                }
            }

        print(f"Found pricing row: {pricing_row}")

        # Get base hourly cost
        base_hourly_cost = float(pricing_row['hourly_cost'])

        # Apply package multiplier - map package names to CSV column names
        package_name_mapping = {
            "pangolin": "package_multiplier_pangolin",
            "pangolin+": "package_multiplier_pangolin+",
            "pangolin+ai": "package_multiplier_pangolin+AI",
            "coolify": "package_multiplier_coolify",
            "coolify+": "package_multiplier_coolify+"
        }

        package_multiplier_key = package_name_mapping.get(package.lower(), "package_multiplier_pangolin")
        package_multiplier_value = pricing_row.get(package_multiplier_key, 1.0)
        package_multiplier = float(package_multiplier_value) if package_multiplier_value is not None else 1.0

        # Get support cost
        support_level_num = str(support_level).split()[-1]  # Extract level number
        support_cost_key = f"support_cost_level{support_level_num}"
        support_cost_value = pricing_row.get(support_cost_key, 0.0)
        # Handle None values that might occur from CSV parsing issues
        support_cost = float(support_cost_value) if support_cost_value is not None else 0.0

        # Calculate costs
        instance_cost = base_hourly_cost * package_multiplier
        hourly_cost = instance_cost + support_cost
        monthly_cost = hourly_cost * 24 * 30

        daily_cost = hourly_cost * 24

        result = {
            "hourly_cost": round(hourly_cost, 4),
            "daily_cost": round(daily_cost, 2),
            "monthly_cost": round(monthly_cost, 2),
            "breakdown": {
                "instance_cost": round(instance_cost, 4),
                "storage_cost": 0.0,
                "network_cost": 0.0,
                "support_cost": round(support_cost, 4)
            }
        }

        print(f"Calculated pricing: {result}")
        return result

# Create singleton instance
pricing_service = PricingService()
