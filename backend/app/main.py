from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from app.api import users, deployments, auth
from app.api import servers
from app.api import billing
from app.api import stripe_payments
from app.api import referrals
from app.api import signup_credits
from app.api import support
from app.core.config import get_settings
import time
from collections import defaultdict
from typing import Dict, List

app = FastAPI(
    title="Manidae Cloud API",
    description="API for managing deployments and users on the Manidae Cloud platform.",
    version="0.1.0",
)

# Add CORS middleware - configurable via environment
settings = get_settings()
cors_origins = [origin.strip() for origin in settings.CORS_ORIGINS.split(",")]
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory rate limiter
request_counts: Dict[str, List[float]] = defaultdict(list)
RATE_LIMIT = 200  # requests (increased from 100)
RATE_WINDOW = 60  # seconds

# Exempt certain endpoints from rate limiting
RATE_LIMIT_EXEMPT_PATHS = {
    "/api/auth/verify-email",
    "/api/auth/reset-password",
    "/api/auth/forgot-password"
}

@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    # Skip rate limiting for exempt paths
    if request.url.path in RATE_LIMIT_EXEMPT_PATHS:
        return await call_next(request)

    client_ip = request.client.host if request.client else "unknown"
    now = time.time()

    # Clean old requests
    request_counts[client_ip] = [t for t in request_counts[client_ip] if now - t < RATE_WINDOW]

    # Check limit
    if len(request_counts[client_ip]) >= RATE_LIMIT:
        raise HTTPException(status_code=429, detail="Rate limit exceeded")

    # Add current request
    request_counts[client_ip].append(now)

    return await call_next(request)

app.include_router(users.router, prefix="/api/users", tags=["users"])
app.include_router(deployments.router, prefix="/api/deployments", tags=["deployments"])
app.include_router(servers.router, prefix="/api/servers", tags=["servers"])
app.include_router(auth.router, prefix="/api/auth", tags=["auth"])
app.include_router(billing.router, prefix="/api/billing", tags=["billing"])
app.include_router(stripe_payments.router, prefix="/api/stripe", tags=["stripe"])
app.include_router(referrals.router, prefix="/api/referrals", tags=["referrals"])
app.include_router(signup_credits.router, prefix="/api", tags=["signup-credits"])
app.include_router(support.router, prefix="/api/support", tags=["support"])

@app.get("/")
def read_root():
    return {"Hello": "World"}
