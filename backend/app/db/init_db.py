
from sqlalchemy import create_engine

from app.db.base import Base
from app.models.user import User
from app.models.deployment import Deployment
from app.models.transaction import Transaction
from app.models.instance_pricing import InstancePricing
from app.models.referral_credit import ReferralCredit
from app.models.signup_credit import SignupCredit
from app.core.config import get_settings

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.core.security import get_password_hash
from app.db.load_pricing_data import load_pricing_data


def init_db():
    engine = create_engine(get_settings().DATABASE_URL)

    db: Session = SessionLocal()
    try:
        # Check if there's at least one admin user in existing database
        admin_count = db.query(User).filter(User.is_admin == True).count()
        user_count = db.query(User).count()
        
        if user_count > 0 and admin_count == 0:
            # If users exist but no admin, make the first user an admin
            first_user = db.query(User).first()
            if first_user:
                first_user.is_admin = True
                db.commit()
                print(f"Made existing user '{first_user.username}' an administrator.")
        
        if user_count > 0:
            print(f"Database has {user_count} users, {admin_count if admin_count > 0 else 1} of which are administrators.")
        else:
            print("Database initialized. The first user to register will become an administrator.")

        # Load pricing data
        load_pricing_data()

    finally:
        db.close()

if __name__ == "__main__":
    init_db()
