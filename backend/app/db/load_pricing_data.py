import csv
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import app modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.db.session import SessionLocal
from app.models.instance_pricing import InstancePricing

def load_pricing_data(update_mode=True):
    """
    Load pricing data from CSV into the database

    Args:
        update_mode (bool): If True, update existing rows and add new ones.
                           If False, clear all data and reload (legacy behavior).
    """
    # Fix the path to look for the CSV file in the root config folder
    csv_path = Path(__file__).parent.parent.parent / "config" / "pricing.csv"
    if not os.path.exists(csv_path):
        print(f"Error: Pricing CSV file not found at {csv_path}")
        return

    db = SessionLocal()
    try:
        if not update_mode:
            # Legacy behavior: Clear existing pricing data
            print("Clearing all existing pricing data...")
            db.query(InstancePricing).delete()

        # Load data from CSV
        with open(csv_path, 'r') as file:
            reader = csv.DictReader(file)
            updated_count = 0
            added_count = 0

            for row in reader:
                # Convert active column to boolean (1 = True, 0 = False)
                active_value = bool(int(row.get('active', '1')))

                # Create the data dictionary for this row
                row_data = {
                    'cloud_provider': row['cloud_provider'],
                    'instance_type': row['instance_type'],
                    'region': row['region'],
                    'cost_per_hour': float(row['hourly_cost']),
                    'package_multiplier_pangolin': float(row['package_multiplier_pangolin']),
                    'package_multiplier_pangolin_plus': float(row['package_multiplier_pangolin+']),
                    'package_multiplier_pangolin_plus_ai': float(row['package_multiplier_pangolin+AI']),
                    'package_multiplier_coolify': float(row['package_multiplier_coolify']),
                    'support_cost_level1': float(row['support_cost_level1']),
                    'support_cost_level2': float(row['support_cost_level2']),
                    'support_cost_level3': float(row['support_cost_level3']),
                    'active': active_value,
                    'cpu': float(row.get('cpu', 1)),
                    'memory': float(row.get('memory', 1)),
                    'country_code': row.get('country_code', '')
                }

                if update_mode:
                    # Try to find existing entry
                    existing = db.query(InstancePricing).filter(
                        InstancePricing.cloud_provider == row_data['cloud_provider'],
                        InstancePricing.instance_type == row_data['instance_type'],
                        InstancePricing.region == row_data['region']
                    ).first()

                    if existing:
                        # Check if any values have changed
                        has_changes = False
                        for key, value in row_data.items():
                            if getattr(existing, key) != value:
                                setattr(existing, key, value)
                                has_changes = True

                        if has_changes:
                            updated_count += 1
                            print(f"Updated: {row_data['cloud_provider']} {row_data['instance_type']} {row_data['region']}")
                    else:
                        # Create new entry
                        pricing = InstancePricing(**row_data)
                        db.add(pricing)
                        added_count += 1
                        print(f"Added: {row_data['cloud_provider']} {row_data['instance_type']} {row_data['region']}")
                else:
                    # Legacy mode: just add all entries
                    pricing = InstancePricing(**row_data)
                    db.add(pricing)

        db.commit()

        if update_mode:
            print(f"Successfully updated pricing data from {csv_path}")
            print(f"Updated {updated_count} existing entries, added {added_count} new entries")
        else:
            print(f"Successfully loaded pricing data from {csv_path}")

    except Exception as e:
        db.rollback()
        print(f"Error loading pricing data: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Load pricing data from CSV into database')
    parser.add_argument('--clear', action='store_true',
                       help='Clear all existing data before loading (legacy mode)')
    parser.add_argument('--update', action='store_true', default=True,
                       help='Update existing entries and add new ones (default)')

    args = parser.parse_args()

    # If --clear is specified, use legacy mode (clear all data)
    # Otherwise use update mode (default)
    update_mode = not args.clear

    print(f"Running in {'update' if update_mode else 'clear and reload'} mode...")
    load_pricing_data(update_mode=update_mode)
