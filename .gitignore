# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.egg-info/
.pytest_cache/

# IDEs
.idea/
#.vscode/

# OS
.DS_Store
.Trashes
Thumbs.db

# Environment variables
.env
.env.docker
.env.local
.env.development.local
.env.test.local
.env.production.local
.env-copy

# Frontend
frontend/node_modules
frontend/.pnp
frontend/.pnp.js
frontend/coverage
frontend/build
frontend/dist
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*

# Backend
backend/deployments/*
backend/backend/deployments/*

# Frontend
scripts/monitoring_job/node_modules
scripts/monitoring_job/.pnp
scripts/monitoring_job/.pnp.js
scripts/monitoring_job/coverage
scripts/monitoring_job/build
scripts/monitoring_job/dist
scripts/monitoring_job/npm-debug.log*
scripts/monitoring_job/yarn-debug.log*
scripts/monitoring_job/yarn-error.log*

# Terraform
*.tfstate
*.tfstate.backup
*.tfvars
**/.terraform/
.terraform.lock.hcl
crash.log

# Secrets and keys
**/your-service-account-key.json
**/your-service-account-key.json:Zone.Identifier
keys/

# Go build artifacts
**/bin/
**/dist/
**/build/
**/*.exe
**/*.test
**/*.out
**/go.sum
**/go.work
**/go.work.sum
**/*.zip
**/*.tar.gz

# Provider binaries
**/terraform-provider-komodo-provider*
**/terraform-provider-contextware*

TBD_*

# Scripts
scripts/node_modules
scripts/.pnp
scripts/.pnp.js
scripts/coverage
scripts/build
scripts/dist
scripts/npm-debug.log*
scripts/yarn-debug.log*
scripts/yarn-error.log*
