# Docker Guide for Manidae Cloud

This document explains how to build, run, and manage the Manidae Cloud application using the provided Docker setup.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/) installed and running.
- [Docker Compose](https://docs.docker.com/compose/install/) (included with Docker Desktop).

## Project Structure

The Docker setup consists of five main services defined in `docker-compose.yml`:

1.  `db`: The PostgreSQL database.
2.  `backend`: The FastAPI application.
3.  `frontend`: The React application served via Nginx.
4.  `billing_job`: A container that runs a recurring billing script every 10 minutes.
5.  `bandwidth_monitor`: A Node.js container that monitors cloud provider bandwidth usage every hour.

## First-Time Setup

1.  **Configure Environment:**
    -   Navigate to the `backend/` directory.
    -   If it doesn't exist, copy the `.env.example` to a new file named `.env`.
    -   Edit `backend/.env` and fill in the required values, especially `POSTGRES_PASSWORD` and `SECRET_KEY`.
    -   **Important:** Ensure the `DATABASE_URL` in `backend/.env` points to the `db` service (e.g., `DATABASE_URL=**********************************************/manidae`). This allows the backend container to connect to the database container within the Docker network.

2.  **Database Initialization:**
    -   The database is initialized automatically when the backend container starts. It runs the `backend/app/db/init_db.py` script to create tables and perform initial data setup. No manual steps are needed.

3.  **Bandwidth Monitor Setup (Optional):**
    -   If you want to enable bandwidth monitoring, configure the monitoring service:
        ```bash
        cd scripts
        ./setup-docker.sh
        ```
    -   Edit `scripts/.env.docker` with your cloud provider API credentials
    -   For GCP: Place your service account key file in `scripts/gcp-keys/`

4.  **Make Scripts Executable:**
    -   In the project root, run the following command to make the helper scripts executable:
        ```bash
        chmod +x build_images.sh push_images.sh scripts/setup-docker.sh
        ```

## Running the Application Locally

To start all the services (frontend, backend, database) for local development, run the following command from the project root:

```bash
docker compose up -d
```

-   The `-d` flag runs the containers in detached mode (in the background).
-   The application will be available at `http://localhost:5173`.
-   The backend API will be available at `http://localhost:8000`.
-   Changes to your local code in `backend/app/` will trigger an automatic reload of the FastAPI server. You will need to manually restart the frontend container to see changes.

To see the logs from all running containers:

```bash
docker compose logs -f
```

To stop and remove all the containers, networks, and volumes:

```bash
docker compose down -v
```

## Managing the Bandwidth Monitor

The bandwidth monitoring service runs independently and can be managed separately:

### Starting Only the Bandwidth Monitor

```bash
# Start just the bandwidth monitor
docker compose up -d bandwidth_monitor

# View bandwidth monitor logs
docker compose logs -f bandwidth_monitor
```

### Testing the Bandwidth Monitor

```bash
# Test the configuration without starting the service
docker compose run --rm bandwidth_monitor node -e "console.log('Configuration test successful')"

# Run a single monitoring cycle
docker compose run --rm bandwidth_monitor node monitor.js
```

### Bandwidth Monitor Configuration

The bandwidth monitor uses separate configuration files for security:

- **Configuration**: `scripts/.env.docker` (API credentials and thresholds)
- **Data Storage**: Docker volume `bandwidth_data` (persistent usage tracking)
- **GCP Keys**: `scripts/gcp-keys/` directory (mounted read-only)

### Monitoring Frequency

- **Default**: Runs every hour (3600 seconds)
- **Billing Job**: Runs every 10 minutes (600 seconds)
- **Customization**: Edit the `sleep` value in `docker-compose.yml`

### Security Features

- **Isolated Environment**: Separate container with minimal dependencies
- **Non-root User**: Runs as user `monitor` (UID 1001)
- **Read-only Mounts**: GCP credentials mounted read-only
- **Health Checks**: Automatic container health monitoring

## Building and Pushing Images for Production

When you want to create production-ready images and push them to a container registry (like Docker Hub), use the provided scripts.

1.  **Update Registry Information:**
    -   Open `build_images.sh` and `push_images.sh`.
    -   Replace the placeholder `your_dockerhub_username` with your actual Docker Hub username or the URL of your private registry.

2.  **Log in to Docker:**
    -   If you are pushing to Docker Hub, log in first:
        ```bash
        docker login
        ```

3.  **Build the Images:**
    -   Run the build script from the project root:
        ```bash
        ./build_images.sh
        ```
    -   This will build the images and tag them appropriately (e.g., `your_dockerhub_username/manidae-backend:latest`).

4.  **Push the Images:**
    -   Run the push script from the project root:
        ```bash
        ./push_images.sh
        ```
    -   This will upload your images to the configured registry.
