# Manidae Cloud - Gemini Development Plan

This document outlines the development plan for the Manidae Cloud managed hosting platform, as executed by the Gemini agent.

## 1. Project Overview

The goal is to build a simplified managed hosting platform that allows users to deploy pre-configured open-source software packages on cloud infrastructure. The platform will handle infrastructure provisioning, software deployment, and billing.

## 2. Key Decisions Log

- **Backend Framework:** Python with FastAPI.
- **Frontend Framework:** React with TypeScript and Tailwind CSS.
- **Core Application Images:** "Pangolin" and "Middleware Manager" are pre-existing Docker images.
- **Komodo Architecture:** The platform will interact with a central, multi-tenant Komodo server. The "Deluxe" package also includes a separate, client-side Komodo container.
- **Cloud Credentials:** The platform will initially use its own master cloud credentials. Costs will be passed on to users with a margin.
- **Pricing Model:** A configuration table will store base costs for cloud instances, and a margin will be applied to calculate the final user price.
- **GitHub Integration:** A Personal Access Token (PAT) will be used for backend GitHub operations.

## 3. Development Phases

### Phase 1: MVP - Core Deployment Functionality (In Progress)

**Goal:** Achieve a complete, end-to-end deployment flow for the "Basic Package" on Google Cloud.

**Task Checklist:**
- [ ] **Architecture:** Set up project structure for backend (FastAPI) and frontend (React).
- [ ] **Database:**
    - [ ] Initialize PostgreSQL database.
    - [ ] Define and create `Users` table.
    - [ ] Define and create `Deployments` table.
    - [ ] Define and create `InstancePricing` table for cost calculations.
- [ ] **Backend (FastAPI):**
    - [ ] Implement User Registration & Login (JWT).
    - [ ] API Endpoint: `GET /api/packages`
    - [ ] API Endpoint: `GET /api/cloud-providers`
    - [ ] API Endpoint: `GET /api/regions/:provider`
    - [ ] API Endpoint: `GET /api/instance-types/:provider/:region`
    - [ ] API Endpoint: `POST /api/pricing/calculate`
    - [ ] API Endpoint: `POST /api/deployments` (Core deployment logic)
    - [ ] Develop Deployment Engine to generate Terraform and Komodo configurations.
- [ ] **Frontend (React):**
    - [ ] Build Step 1: Package Selection UI.
    - [ ] Build Step 2: Cloud Provider & Instance Selection UI.
    - [ ] Build Step 3: Support Level Selection UI (simplified for MVP).
    - [ ] Build Step 4: Configuration & "Deploy" UI (no payment for MVP).
    - [ ] Connect UI to all required backend APIs.
- [ ] **Edge Case Handling:**
    - [ ] Edge Case 1: Validate domain meets the fqdn standard like context.ai (no subdomain)
    - [ ] Edge Case 2: Validate the admin email is a email format
    - [ ] Edge Case 5: Validate the postgres_host - it must be set to either pangolin-postgres (if using package pangolin package) or komodo-postgres-1 (if using a package with komodo)
    - [ ] Edge Case 6: Validate package - it must be either Pangolin, Mid, or Deluxe
- [x] **Premium Package Integration & Terraform Init Issue:**
    - Attempted to add "Premium" package with CrowdSec and static page.
    - Encountered persistent `terraform init` failures due to precise string matching requirements for `komodo_resource_block` in `backend/app/core/deployment/deployment_manager.py`.
    - The issue stems from Jinja2's rendering of `main.tf.j2` and the exact whitespace/newline characters, which caused the commenting/uncommenting logic to fail. Multiple attempts to precisely match the string literal were unsuccessful.



### Phase 2: Enhanced Features & User Dashboard

**Goal:** Introduce billing, a user dashboard, and support for all packages/tiers.

**Task Checklist:**
- [ ] **Billing:** Integrate Stripe for payments and subscriptions.
- [ ] **Pricing:** Implement the full, real-time pricing calculator.
- [ ] **User Dashboard:**
    - [ ] Build UI to list active deployments.
    - [ ] Build UI for billing history.
    - [ ] Build UI for support ticket management.
- [ ] **Deployment Engine:**
    - [ ] Add logic for "Mid" and "Deluxe" packages.
    - [ ] Add logic for multiple support tiers.
- [ ] **API:** Implement `GET /api/deployments/:id/status` for real-time status checks.

### Phase 3: Scale & Polish

**Goal:** Harden the platform, improve security, and prepare for expansion.

**Task Checklist:**
- [ ] **Multi-Cloud:** Refactor code to support adding AWS and Azure.
- [ ] **Security:**
    - [ ] Implement Multi-Factor Authentication (MFA).
    - [ ] Conduct security audit and apply hardening measures.
- [ ] **Operations:**
    - [ ] Set up robust monitoring, logging, and alerting.
    - [ ] Perform load testing and performance optimization.
- [ ] **Features:** Implement rollback capability (`ProcedureDestroy`).
