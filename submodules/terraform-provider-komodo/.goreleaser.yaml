version: 2

project_name: terraform-provider-komodo

release:
  github:
    owner: oide<PERSON>tt
    name: terraform-provider-komodo

builds:
  - binary: terraform-provider-komodo
    goos: [linux, darwin, windows]
    goarch: [amd64, arm64]
    ldflags: "-s -w -X main.version={{.Version}}"
    # Remove the -mod=vendor flag since we'll fix the vendoring issue
    # flags: ["-mod=vendor"]

archives:
  - format: zip
    name_template: "{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
    files:
      - LICENSE

checksum:
  name_template: "{{ .ProjectName }}_{{ .Version }}_SHA256SUMS"

signs:
  - artifacts: checksum
    args: ["--batch", "--local-user", "{{ .Env.GPG_FINGERPRINT }}", "--output", "${signature}", "--detach-sign", "--armor", "${artifact}"]
    cmd: gpg
