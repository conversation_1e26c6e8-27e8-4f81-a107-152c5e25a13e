[[stack]]
name = "${client_name_lower}-setup"
[stack.config]
server = "server-${client_name_lower}"
repo = "oidebrett/getcontextware"
file_paths = ["docker-compose-setup.yml"]
environment = """
DOMAIN=${domain}
EMAIL=${email}
ADMIN_USERNAME=${admin_username}
ADMIN_PASSWORD=${admin_password}
ADMIN_SUBDOMAIN=${admin_subdomain}
CROWDSEC_ENROLLMENT_KEY=${crowdsec_key}
POSTGRES_USER=${postgres_user}
POSTGRES_PASSWORD=${postgres_password}
POSTGRES_HOST=${postgres_host}
STATIC_PAGE=${static_page}
CLIENT_ID=${client_id}
CLIENT_SECRET=${client_secret}
"""

[[stack]]
name = "${client_name_lower}-stack"
[stack.config]
server = "server-${client_name_lower}"
files_on_host = true
run_directory = "/etc/komodo/stacks/${client_name_lower}-setup"

[[procedure]]
name = "ProcedureApply"
description = "This procedure runs the initial setup that write out a compose file for the main stack deployment"

[[procedure.config.stage]]
name = "Setup"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "${client_name_lower}-setup", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "Wait For Compose Write"
enabled = true
executions = [
  { execution.type = "Sleep", execution.params.duration_ms = 10000, enabled = true }
]

[[procedure.config.stage]]
name = "Stack"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "${client_name_lower}-stack", execution.params.services = [], enabled = true }
]

[[procedure]]
name = "ProcedureDestroy"

[[procedure.config.stage]]
name = "Stack"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "${client_name_lower}-stack", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]

[[procedure.config.stage]]
name = "Setup"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "${client_name_lower}-setup", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]
