[[stack]]
name = "${client_name_lower}-setup"
[stack.config]
server = "server-${client_name_lower}"
repo = "${github_repo}"
file_paths = ["docker-compose-setup.yml"]
environment = """
DOMAIN=${domain}
EMAIL=${admin_email}
ADMIN_USERNAME=${admin_username}
ADMIN_PASSWORD=${admin_password}
ADMIN_SUBDOMAIN=${admin_subdomain}
"""

[[stack]]
name = "${client_name_lower}-stack"
[stack.config]
server = "server-${client_name_lower}"
files_on_host = true
run_directory = "/etc/komodo/stacks/${client_name_lower}-setup"

[[procedure]]
name = "ProcedureApply"
description = "This procedure runs the initial setup"

[[procedure.config.stage]]
name = "Setup"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "${client_name_lower}-setup", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "Stack"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "${client_name_lower}-stack", execution.params.services = [], enabled = true }
]

[[procedure]]
name = "ProcedureDestroy"

[[procedure.config.stage]]
name = "Stack"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "${client_name_lower}-stack", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]

[[procedure.config.stage]]
name = "Setup"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "${client_name_lower}-setup", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]
