{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@tailwindcss/postcss": "^4.1.12", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^20.15.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.539.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "vite", "build": "tsc && vite build", "test": "vitest", "serve": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "vite": "^5.4.0", "vitest": "^2.0.4"}}