import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Deployment, DeploymentCreate, DeploymentDeleteResponse, CloudProvider, Region, InstanceType, Package, PricingCalculation } from '../api/types';
import { apiClient } from '../api';

interface DeploymentState {
  deployments: Deployment[];
  packages: Package[];
  cloudProviders: CloudProvider[];
  regions: Region[];
  instanceTypes: InstanceType[];
  currentPricing: PricingCalculation | null;
  isLoading: boolean;
  error: string | null;
}

type DeploymentAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SET_DEPLOYMENTS'; payload: Deployment[] }
  | { type: 'SILENT_UPDATE_DEPLOYMENTS'; payload: Deployment[] }
  | { type: 'ADD_DEPLOYMENT'; payload: Deployment }
  | { type: 'REMOVE_DEPLOYMENT'; payload: Deployment | DeploymentDeleteResponse }
  | { type: 'UPDATE_DEPLOYMENT_STATUS'; payload: { id: number; status: string } }
  | { type: 'UPDATE_DEPLOYMENT'; payload: Deployment }
  | { type: 'SET_PACKAGES'; payload: Package[] }
  | { type: 'SET_CLOUD_PROVIDERS'; payload: CloudProvider[] }
  | { type: 'SET_REGIONS'; payload: Region[] }
  | { type: 'SET_INSTANCE_TYPES'; payload: InstanceType[] }
  | { type: 'SET_PRICING'; payload: PricingCalculation | null };

const initialState: DeploymentState = {
  deployments: [],
  packages: [],
  cloudProviders: [],
  regions: [],
  instanceTypes: [],
  currentPricing: null,
  isLoading: false,
  error: null,
};

const deploymentReducer = (state: DeploymentState, action: DeploymentAction): DeploymentState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    case 'SET_DEPLOYMENTS':
      return {
        ...state,
        deployments: action.payload.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()),
        isLoading: false
      };
    case 'SILENT_UPDATE_DEPLOYMENTS':
      return {
        ...state,
        deployments: action.payload.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        // Note: Don't change isLoading state for silent updates
      };
    case 'ADD_DEPLOYMENT':
      return {
        ...state,
        deployments: [action.payload, ...state.deployments],
        isLoading: false
      };
    case 'REMOVE_DEPLOYMENT':
      // If payload has a message (permanent deletion), remove from list
      if ('message' in action.payload && action.payload.message) {
        const deleteResponse = action.payload as DeploymentDeleteResponse;
        return {
          ...state,
          deployments: state.deployments.filter(d => d.id !== deleteResponse.deploymentId),
          isLoading: false
        };
      }
      // Otherwise, it's a deactivation (update status)
      const deployment = action.payload as Deployment;
      return {
        ...state,
        deployments: state.deployments.map(d =>
          d.id === deployment.id ? { ...d, status: deployment.status, deleted_at: deployment.deleted_at } : d
        ),
        isLoading: false
      };
    case 'UPDATE_DEPLOYMENT_STATUS':
      return {
        ...state,
        deployments: state.deployments.map(d =>
          d.id === action.payload.id ? { ...d, status: action.payload.status } : d
        ),
      };
    case 'UPDATE_DEPLOYMENT':
      return {
        ...state,
        deployments: state.deployments.map(d =>
          d.id === action.payload.id ? action.payload : d
        ),
      };
    case 'SET_PACKAGES':
      return { ...state, packages: action.payload };
    case 'SET_CLOUD_PROVIDERS':
      return { ...state, cloudProviders: action.payload };
    case 'SET_REGIONS':
      return { ...state, regions: action.payload };
    case 'SET_INSTANCE_TYPES':
      return { ...state, instanceTypes: action.payload };
    case 'SET_PRICING':
      return { ...state, currentPricing: action.payload };
    default:
      return state;
  }
};

interface DeploymentContextType extends DeploymentState {
  fetchDeployments: (userId?: number) => Promise<void>;
  silentFetchDeployments: (userId?: number) => Promise<Deployment[]>;
  silentUpdateDeployments: (userId?: number) => Promise<void>;
  createDeployment: (deploymentData: DeploymentCreate, userEmail: string, username: string) => Promise<Deployment>;
  deleteDeployment: (deploymentId: number) => Promise<void>;
  pollDeploymentStatus: (deploymentId: number) => Promise<void>;
  fetchPackages: () => Promise<void>;
  fetchCloudProviders: () => Promise<void>;
  fetchRegions: (provider: string) => Promise<void>;
  fetchInstanceTypes: (provider: string, region: string) => Promise<void>;
  calculatePricing: (deploymentData: DeploymentCreate) => Promise<void>;
  clearError: () => void;
  clearPricing: () => void;
}

const DeploymentContext = createContext<DeploymentContextType | undefined>(undefined);

export const useDeployment = () => {
  const context = useContext(DeploymentContext);
  if (context === undefined) {
    throw new Error('useDeployment must be used within a DeploymentProvider');
  }
  return context;
};

interface DeploymentProviderProps {
  children: ReactNode;
}

export const DeploymentProvider: React.FC<DeploymentProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(deploymentReducer, initialState);

  const fetchDeployments = async (userId?: number): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      const deployments = userId
        ? await apiClient.getDeploymentsByUser(userId)
        : await apiClient.getDeployments();

      dispatch({ type: 'SET_DEPLOYMENTS', payload: deployments });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to fetch deployments'
      });
    }
  };

  const silentFetchDeployments = async (userId?: number): Promise<Deployment[]> => {
    // Silent fetch without loading states or error handling for background polling
    try {
      const deployments = userId
        ? await apiClient.getDeploymentsByUser(userId)
        : await apiClient.getDeployments();

      return deployments;
    } catch (error) {
      console.error('Silent fetch failed:', error);
      return []; // Return empty array on error
    }
  };

  const silentUpdateDeployments = async (userId?: number): Promise<void> => {
    // Silent update that only updates state if there are changes
    try {
      const newDeployments = await silentFetchDeployments(userId);

      // Only update if we got data and there are actual changes
      if (newDeployments.length > 0) {
        const hasChanges = state.deployments.some((current) => {
          const updated = newDeployments.find(d => d.id === current.id);
          if (!updated) return true; // Deployment was removed

          // Check for status or other important field changes
          return (
            current.status !== updated.status ||
            current.deleted_at !== updated.deleted_at ||
            current.instance_ip !== updated.instance_ip ||
            current.vps_ip_address !== updated.vps_ip_address
          );
        });

        // Also check if new deployments were added
        const newDeploymentIds = newDeployments.filter(updated =>
          !state.deployments.find(current => current.id === updated.id)
        );

        if (hasChanges || newDeploymentIds.length > 0) {
          dispatch({ type: 'SILENT_UPDATE_DEPLOYMENTS', payload: newDeployments });
        }
      }
    } catch (error) {
      console.error('Silent update failed:', error);
      // Don't dispatch error for silent updates
    }
  };

  const createDeployment = async (deploymentData: DeploymentCreate, userEmail: string, username: string): Promise<Deployment> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      const deployment = await apiClient.createDeployment(deploymentData, userEmail, username);
      console.log('🚀 Created deployment:', deployment);
      dispatch({ type: 'ADD_DEPLOYMENT', payload: deployment });
      pollDeploymentStatus(deployment.id); // Start polling for status updates
      return deployment;
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to create deployment'
      });
      throw error;
    }
  };

  const pollDeploymentStatus = async (deploymentId: number): Promise<void> => {
    console.log(`🔄 Starting polling for deployment ${deploymentId}`);

    // Function to check status
    const checkStatus = async () => {
      try {
        const status = await apiClient.getDeploymentStatus(deploymentId);
        console.log(`📊 Deployment ${deploymentId} status: ${status}`);

        dispatch({ type: 'UPDATE_DEPLOYMENT_STATUS', payload: { id: deploymentId, status } });

        // Fetch full deployment data when infrastructure is ready or DNS validation completes
        if (['ACTIVE', 'DNS_VALIDATION', 'READY', 'DNS_FAILED'].includes(status)) {
          console.log(`✅ Deployment ${deploymentId} status is ${status}, fetching full data...`);
          try {
            const deployments = await apiClient.getDeployments();
            const updatedDeployment = deployments.find(d => d.id === deploymentId);
            if (updatedDeployment) {
              console.log(`📦 Updated deployment data:`, updatedDeployment);
              dispatch({ type: 'UPDATE_DEPLOYMENT', payload: updatedDeployment });
            }
          } catch (fetchError) {
            console.error(`Failed to fetch updated deployment data for ${deploymentId}:`, fetchError);
          }
        }

        // Return true if we should continue polling
        return ['CREATING', 'PROVISIONING', 'DNS_VALIDATION'].includes(status);
      } catch (error) {
        console.error(`Failed to poll status for deployment ${deploymentId}:`, error);
        return false; // Stop polling on error
      }
    };

    // Check status immediately
    const shouldContinue = await checkStatus();

    if (shouldContinue) {
      // Set up interval for subsequent checks
      const interval = setInterval(async () => {
        const shouldContinue = await checkStatus();
        if (!shouldContinue) {
          console.log(`🛑 Stopping polling for deployment ${deploymentId}`);
          clearInterval(interval);
        }
      }, 5000); // Poll every 5 seconds
    }
  };

  const deleteDeployment = async (deploymentId: number): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      const result = await apiClient.deleteDeployment(deploymentId);

      // Check if it's a permanent deletion (returns message) or deactivation (returns deployment)
      if ('message' in result && result.message) {
        // Permanent deletion - remove from list
        dispatch({ type: 'REMOVE_DEPLOYMENT', payload: { message: result.message, deploymentId } });
      } else {
        // Deactivation - update deployment status
        dispatch({ type: 'REMOVE_DEPLOYMENT', payload: result as Deployment });
      }
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to delete deployment'
      });
    }
  };

  const fetchPackages = async (): Promise<void> => {
    try {
      const packages = await apiClient.getPackages();
      dispatch({ type: 'SET_PACKAGES', payload: packages });
    } catch (error) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : 'Failed to fetch packages' 
      });
    }
  };

  const fetchCloudProviders = async (serverType: string = "new"): Promise<void> => {
    try {
      const providers = await apiClient.getCloudProviders();
      dispatch({ type: 'SET_CLOUD_PROVIDERS', payload: providers });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to fetch cloud providers'
      });
    }
  };

  const fetchRegions = async (provider: string): Promise<void> => {
    try {
      const regions = await apiClient.getRegions(provider);
      dispatch({ type: 'SET_REGIONS', payload: regions });
    } catch (error) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : 'Failed to fetch regions' 
      });
    }
  };

  const fetchInstanceTypes = async (provider: string, region: string): Promise<void> => {
    try {
      const instanceTypes = await apiClient.getInstanceTypes(provider, region);
      dispatch({ type: 'SET_INSTANCE_TYPES', payload: instanceTypes });
    } catch (error) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : 'Failed to fetch instance types' 
      });
    }
  };

  const calculatePricing = async (deploymentData: DeploymentCreate): Promise<void> => {
    try {
      const pricing = await apiClient.calculatePricing(deploymentData);
      dispatch({ type: 'SET_PRICING', payload: pricing });
    } catch (error) {
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : 'Failed to calculate pricing' 
      });
    }
  };

  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const clearPricing = (): void => {
    dispatch({ type: 'SET_PRICING', payload: null });
  };

  const value: DeploymentContextType = {
    ...state,
    fetchDeployments,
    silentFetchDeployments,
    silentUpdateDeployments,
    createDeployment,
    deleteDeployment,
    pollDeploymentStatus,
    fetchPackages,
    fetchCloudProviders,
    fetchRegions,
    fetchInstanceTypes,
    calculatePricing,
    clearError,
    clearPricing,
  };

  return <DeploymentContext.Provider value={value}>{children}</DeploymentContext.Provider>;
};
