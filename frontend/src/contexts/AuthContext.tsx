import React, { createContext, useContext, useReducer, ReactNode, useEffect, useCallback } from 'react';
import { User, UserCreate } from '../api/types';
import { apiClient } from '../api';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'CLEAR_ERROR' }
  | { type: 'LOGOUT' };

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        error: null,
      };
    default:
      return state;
  }
};

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  signup: (userData: UserCreate) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load user from localStorage on app start
  useEffect(() => {
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser);
        dispatch({ type: 'SET_USER', payload: user });
      } catch (error) {
        localStorage.removeItem('user');
      }
    }
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      // Client-side password validation
      if (password.length <= 6) {
        throw new Error('Password must be longer than 6 characters');
      }

      // Authenticate with backend API
      const authResponse = await apiClient.login(email, password);

      // Store the access token
      localStorage.setItem('token', authResponse.access_token);

      // Get user info using the token
      const user = await apiClient.getCurrentUser();

      // Check if user is deactivated (this should not happen as backend blocks login)
      if (user.username.startsWith('deactivated_')) {
        // Clear any stored tokens/data
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        throw new Error('Account has been deactivated');
      }

      // Store user data and complete login
      dispatch({ type: 'SET_USER', payload: user });
      localStorage.setItem('user', JSON.stringify(user));

    } catch (error: any) {
      // Handle specific error messages from the backend
      const errorMessage = error instanceof Error ? error.message : 'Login failed';

      if (errorMessage === 'Account has been deactivated') {
        dispatch({
          type: 'SET_ERROR',
          payload: 'Your account has been deactivated. Please contact support if you need assistance.'
        });
      } else {
        dispatch({
          type: 'SET_ERROR',
          payload: errorMessage
        });
      }
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const signup = async (userData: UserCreate): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      await apiClient.createUser(userData);
      // After successful signup, automatically log in the user
      await login(userData.email, userData.password);
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Signup failed'
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const logout = (): void => {
    // Try to call logout API if available
    try {
      apiClient.logout().catch(() => {
        // Ignore logout API errors
      });
    } catch (error) {
      // Ignore logout API errors
    }

    dispatch({ type: 'LOGOUT' });
    localStorage.removeItem('user');
    localStorage.removeItem('token');
  };

  const clearError = useCallback((): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  const refreshUser = async (): Promise<void> => {
    try {
      const fresh = await apiClient.getCurrentUser();
      dispatch({ type: 'SET_USER', payload: fresh });
      localStorage.setItem('user', JSON.stringify(fresh));
    } catch (e) {
      // ignore
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    signup,
    logout,
    clearError,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};