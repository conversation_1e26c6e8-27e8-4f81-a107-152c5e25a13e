// Utility functions for country flags

/**
 * Convert country code to flag emoji
 * @param countryCode - ISO 3166-1 alpha-2 country code (e.g., 'us', 'de', 'gb')
 * @returns Flag emoji string
 */
export function getCountryFlag(countryCode: string): string {
  if (!countryCode) return '🌍'; // Default globe emoji for unknown/global
  
  // Special cases
  const specialCases: Record<string, string> = {
    'un': '🌍', // United Nations / Global
    'gb': '🇬🇧', // Great Britain
    'uk': '🇬🇧', // United Kingdom (alias for GB)
  };
  
  const code = countryCode.toLowerCase();
  
  if (specialCases[code]) {
    return specialCases[code];
  }
  
  // Convert country code to flag emoji
  // Flag emojis are created by combining regional indicator symbols
  // A = U+1F1E6, B = U+1F1E7, etc.
  try {
    const codePoints = code
      .toUpperCase()
      .split('')
      .map(char => 0x1F1E6 + char.charCodeAt(0) - 'A'.charCodeAt(0));
    
    return String.fromCodePoint(...codePoints);
  } catch (error) {
    console.warn(`Failed to generate flag for country code: ${countryCode}`);
    return '🌍'; // Fallback to globe emoji
  }
}

/**
 * Get a readable country name from country code
 * @param countryCode - ISO 3166-1 alpha-2 country code
 * @returns Human-readable country name
 */
export function getCountryName(countryCode: string): string {
  const countryNames: Record<string, string> = {
    'us': 'United States',
    'de': 'Germany',
    'gb': 'United Kingdom',
    'uk': 'United Kingdom',
    'nl': 'Netherlands',
    'fi': 'Finland',
    'sg': 'Singapore',
    'au': 'Australia',
    'ca': 'Canada',
    'in': 'India',
    'ie': 'Ireland',
    'un': 'Global',
  };
  
  return countryNames[countryCode.toLowerCase()] || countryCode.toUpperCase();
}
