// Utility functions for referral program

/**
 * Check if the referral program is enabled based on credit amounts
 * @returns true if referral program should be visible to users
 */
export function isReferralProgramEnabled(): boolean {
  const referralCreditAmount = parseFloat(import.meta.env.VITE_REFERRAL_CREDIT_AMOUNT || '0');
  const referrerCreditAmount = parseFloat(import.meta.env.VITE_REFERRER_CREDIT_AMOUNT || '0');
  
  // Referral program is enabled if either amount is greater than 0
  return referralCreditAmount > 0 || referrerCreditAmount > 0;
}

/**
 * Get referral credit amount for new users (referee)
 * @returns referral credit amount
 */
export function getReferralCreditAmount(): number {
  return parseFloat(import.meta.env.VITE_REFERRAL_CREDIT_AMOUNT || '0');
}

/**
 * Get referrer credit amount for users who refer others
 * @returns referrer credit amount
 */
export function getReferrerCreditAmount(): number {
  return parseFloat(import.meta.env.VITE_REFERRER_CREDIT_AMOUNT || '0');
}
