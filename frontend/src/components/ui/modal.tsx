import React from 'react';

interface ModalProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ open, onClose, title, children }) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm " onClick={onClose} />
      <div className="relative z-10 w-full max-w-2xl rounded-lg border-2 border-border bg-background shadow-2xl ">
        <div className="flex items-center justify-between p-6 border-b-2 border-border bg-card">
          <h3 className="text-lg font-semibold text-foreground ">{title}</h3>
          <button 
            onClick={onClose} 
            className="text-muted-foreground hover:text-foreground transition-colors rounded-full p-2 hover:bg-muted"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 6L6 18" />
              <path d="M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="p-6 bg-card">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;

