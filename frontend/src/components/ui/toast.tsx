import React, { useEffect, useState } from 'react';

export type ToastType = 'success' | 'error' | 'info';

export type ToastOptions = {
  title?: string;
  duration?: number; // ms
};

export type ToastMessage = {
  id: number;
  message: string;
  type: ToastType;
  title?: string;
  duration: number;
};

let listeners: Array<(t: ToastMessage) => void> = [];
let idCounter = 1;

export const toast = (message: string, type: ToastType = 'info', options: ToastOptions = {}) => {
  const toastMsg: ToastMessage = {
    id: idCounter++,
    message,
    type,
    title: options.title,
    duration: options.duration ?? 3500,
  };
  listeners.forEach((l) => l(toastMsg));
};

export const ToasterViewport: React.FC = () => {
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  useEffect(() => {
    const onToast = (t: ToastMessage) => {
      setToasts((prev) => [...prev, t]);
      setTimeout(() => {
        setToasts((prev) => prev.filter((x) => x.id !== t.id));
      }, t.duration);
    };
    listeners.push(onToast);
    return () => {
      listeners = listeners.filter((l) => l !== onToast);
    };
  }, []);

  return (
    <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 flex flex-col items-center space-y-2">
      {toasts.map((t) => (
        <div
          key={t.id}
          className={
            `max-w-sm rounded-md border p-3 shadow-md bg-card text-foreground ${
              t.type === 'success' ? 'border-green-300' : t.type === 'error' ? 'border-red-300' : 'border-border'
            }`
          }
          role="status"
          aria-live="polite"
        >
          {t.title && <div className="text-sm font-medium mb-1">{t.title}</div>}
          <div className="text-sm opacity-90">{t.message}</div>
        </div>
      ))}
    </div>
  );
};

