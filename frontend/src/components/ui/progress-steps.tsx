import React from 'react';

interface Step {
  id: string;
  title: string;
  completed: boolean;
}

interface ProgressStepsProps {
  steps: Step[];
  currentStepId: string;
  className?: string;
}

const ProgressSteps: React.FC<ProgressStepsProps> = ({ steps, currentStepId, className = "" }) => {
  const currentIndex = steps.findIndex(step => step.id === currentStepId);
  const currentStep = steps[currentIndex];
  const completedSteps = steps.filter(step => step.completed).length;
  
  // Calculate progress based on current step position (0-based to 1-based percentage)
  const progressPercentage = ((currentIndex + 1) / steps.length) * 100;
  
  return (
    <div className={`w-full ${className}`}>
      {/* Mobile: Vertical Progress */}
      <div className="block lg:hidden">
        {/* Current Step Header */}
        <div className="bg-card border rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between mb-3">
            <div>
              <h3 className="text-lg font-semibold text-foreground">
                Step {currentIndex + 1}: {currentStep?.title}
              </h3>
              <p className="text-sm text-muted-foreground">
                {completedSteps} of {steps.length} steps completed
              </p>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-12 h-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-bold text-lg">
                {currentIndex + 1}
              </div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-muted rounded-full h-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-500 ease-out" 
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        {/* Step List */}
        <div className="space-y-3">
          {steps.map((step, index) => {
            const isCurrent = step.id === currentStepId;
            const isCompleted = step.completed;
            const isPast = index < currentIndex;
            
            return (
              <div key={step.id} className={`flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${
                isCurrent 
                  ? 'bg-primary/10 border border-primary/20' 
                  : isCompleted 
                    ? 'bg-muted/50' 
                    : 'opacity-60'
              }`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center font-medium text-sm flex-shrink-0 ${
                  isCurrent 
                    ? 'bg-primary text-primary-foreground' 
                    : isCompleted
                      ? 'bg-green-500 text-white'
                      : 'bg-muted text-muted-foreground border border-border'
                }`}>
                  {isCompleted ? (
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 6L9 17l-5-5" />
                    </svg>
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                
                <div className="flex-1">
                  <h4 className={`font-medium ${
                    isCurrent 
                      ? 'text-primary' 
                      : isCompleted 
                        ? 'text-foreground' 
                        : 'text-muted-foreground'
                  }`}>
                    {step.title}
                  </h4>
                  {isCurrent && (
                    <p className="text-sm text-primary/80 mt-1">Current step</p>
                  )}
                  {isCompleted && !isCurrent && (
                    <p className="text-sm text-green-600 mt-1">Completed</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Desktop: Horizontal Progress */}
      <div className="hidden lg:block">
        {/* Current Step Header */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-foreground mb-2">
            {currentStep?.title}
          </h2>
          <p className="text-muted-foreground">
            Step {currentIndex + 1} of {steps.length} • {completedSteps} completed
          </p>
        </div>

        {/* Progress Visualization */}
        <div className="max-w-4xl mx-auto">
          {/* Progress Bar */}
          <div className="relative mb-8">
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-500 ease-out" 
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>

          {/* Step Indicators */}
          <div className="grid grid-cols-3 lg:grid-cols-6 xl:grid-cols-8 gap-4">
            {steps.map((step, index) => {
              const isCurrent = step.id === currentStepId;
              const isCompleted = step.completed;
              
              return (
                <div key={step.id} className={`text-center transition-all duration-200 ${
                  isCurrent ? 'scale-110' : ''
                }`}>
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center font-semibold text-sm mx-auto mb-2 transition-all duration-200 ${
                    isCurrent 
                      ? 'bg-primary text-primary-foreground shadow-lg ring-4 ring-primary/20' 
                      : isCompleted
                        ? 'bg-green-500 text-white shadow-md'
                        : 'bg-muted text-muted-foreground border-2 border-border'
                  }`}>
                    {isCompleted ? (
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                    ) : (
                      <span>{index + 1}</span>
                    )}
                  </div>
                  
                  <h4 className={`text-sm font-medium ${
                    isCurrent 
                      ? 'text-primary font-semibold' 
                      : isCompleted 
                        ? 'text-foreground' 
                        : 'text-muted-foreground'
                  }`}>
                    {step.title}
                  </h4>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressSteps;