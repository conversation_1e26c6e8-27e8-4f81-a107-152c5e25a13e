// @/components/ui/pagination.tsx

import * as React from "react"
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react"
import { Link } from 'react-router-dom';

import { cn } from "@/lib/utils"
import { ButtonProps, buttonVariants } from "@/components/ui/button"

function Pagination({ className, ...props }: React.ComponentProps<"nav">) {
  return (
    <nav
      role="navigation"
      aria-label="pagination"
      className={cn("mx-auto flex w-full justify-center", className)}
      {...props}
    />
  )
}

const PaginationContent = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={cn("flex flex-row items-center gap-1", className)}
    {...props}
  />
))
PaginationContent.displayName = "PaginationContent"

const PaginationItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn("", className)} {...props} />
))
PaginationItem.displayName = "PaginationItem"

type PaginationLinkProps = {
  isActive?: boolean;
  to?: string;
  onClick?: () => void;
} & Pick<ButtonProps, "size">;

const PaginationLink = ({
  className,
  isActive,
  size = "icon",
  to,
  onClick,
  children,
}: PaginationLinkProps & { children?: React.ReactNode; className?: string }) => {
  const baseClassName = cn(
    buttonVariants({
      variant: isActive ? 'outline' : 'ghost',
      size,
    }),
    className
  );

  // If `to` is provided, render a react-router-dom Link.
  if (to) {
    return (
      <Link
        to={to}
        className={baseClassName}
        aria-current={isActive ? "page" : undefined}
      >
        {children}
      </Link>
    );
  }

  // If `onClick` is provided, render a button.
  if (onClick) {
    return (
      <button
        onClick={onClick}
        className={baseClassName}
        aria-current={isActive ? "page" : undefined}
        type="button"
      >
        {children}
      </button>
    );
  }

  // Default to a button if neither is provided.
  return (
    <button
      className={baseClassName}
      aria-current={isActive ? "page" : undefined}
      type="button"
    >
      {children}
    </button>
  );
};

PaginationLink.displayName = "PaginationLink"

const PaginationPrevious = ({
  className,
  ...props
}: Omit<React.ComponentProps<typeof PaginationLink>, 'children'>) => (
  <PaginationLink
    aria-label="Go to previous page"
    className={cn("gap-1 pl-2.5", className)}
    {...props}
  >
    <ChevronLeft className="h-4 w-4" />
    <span>Previous</span>
  </PaginationLink>
)
PaginationPrevious.displayName = "PaginationPrevious"

const PaginationNext = ({
  className,
  ...props
}: Omit<React.ComponentProps<typeof PaginationLink>, 'children'>) => (
  <PaginationLink
    aria-label="Go to next page"
    className={cn("gap-1 pr-2.5", className)}
    {...props}
  >
    <span>Next</span>
    <ChevronRight className="h-4 w-4" />
  </PaginationLink>
)
PaginationNext.displayName = "PaginationNext"

const PaginationEllipsis = ({
  className,
  ...props
}: React.ComponentProps<"span">) => (
  <span
    aria-hidden
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More pages</span>
  </span>
)
PaginationEllipsis.displayName = "PaginationEllipsis"

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
}