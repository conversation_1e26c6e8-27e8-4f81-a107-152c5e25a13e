import React, { useState } from 'react';
import Modal from './ui/modal';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { apiClient } from '../api';

interface PackageRequestModalProps {
  open: boolean;
  onClose: () => void;
}

const PackageRequestModal: React.FC<PackageRequestModalProps> = ({ open, onClose }) => {
  const [packageName, setPackageName] = useState('');
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!packageName.trim() || !reason.trim()) {
      setSubmitMessage('Please fill in both fields');
      return;
    }

    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      const response = await apiClient.submitPackageRequest({
        package_name: packageName.trim(),
        reason: reason.trim()
      });
      
      setSubmitMessage(response.message || 'Request submitted successfully!');
      
      // Clear form after successful submission
      setTimeout(() => {
        setPackageName('');
        setReason('');
        setSubmitMessage('');
        onClose();
      }, 2000);
      
    } catch (error: any) {
      setSubmitMessage(error.message || 'Failed to submit request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setPackageName('');
    setReason('');
    setSubmitMessage('');
    onClose();
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      title="Request a New Package"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Tell us what package you'd like to see added to Manidae Cloud. We're always looking to expand our offerings based on user feedback!
          </p>
          
          <div className="space-y-2">
            <Label htmlFor="package-name">Package Name</Label>
            <Input
              id="package-name"
              type="text"
              placeholder="e.g., WordPress+, NextCloud, GitLab"
              value={packageName}
              onChange={(e) => setPackageName(e.target.value)}
              disabled={isSubmitting}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Why do you want this package?</Label>
            <textarea
              id="reason"
              className="flex min-h-[120px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Describe your use case, what features you need, and how this package would benefit you..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              disabled={isSubmitting}
              required
            />
          </div>

          {submitMessage && (
            <div className={`p-3 rounded-md text-sm ${
              submitMessage.includes('success') || submitMessage.includes('submitted')
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {submitMessage}
            </div>
          )}
        </div>

        <div className="flex justify-end gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !packageName.trim() || !reason.trim()}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Request'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default PackageRequestModal;
