import React, { useState } from 'react';
import { useAuth } from '../contexts';
import { Button } from '../components/ui/button';
import { Card } from '../components/ui/card';
import Modal from '../components/ui/modal';
import { apiClient } from '../api/client';

const AccountSettings: React.FC = () => {
  const { user, logout } = useAuth();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [confirmText, setConfirmText] = useState('');

  const handleDeleteAccount = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // First check if account can be deleted
      const preconditionCheck = await apiClient.checkSelfDeletionPreconditions();

      if (!preconditionCheck.can_delete) {
        setError(`Cannot delete account: You have ${preconditionCheck.active_deployment_count} active deployment(s). Please deactivate all deployments first.`);
        setLoading(false);
        return;
      }

      // Proceed with account deactivation (not deletion for audit trail)
      const result = await apiClient.selfDeleteUser();

      // Show success message and logout
      alert('Your account has been deactivated.\n\nYou will now be logged out.');
      logout();
      window.location.href = '/';
    } catch (err: any) {
      setError(err.message || 'Failed to delete account');
      console.error('Error deleting account:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="p-8">
          <p className="text-center">Please log in to view account settings.</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <h1 className="text-2xl font-bold">Account Settings</h1>

      {error && (
        <Card className="p-4 border-red-200 bg-red-50">
          <p className="text-red-700">{error}</p>
        </Card>
      )}

      {/* Profile Information */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Profile Information</h2>
        <div className="space-y-3">
          <div>
            <label className="text-sm font-medium text-muted-foreground">Username</label>
            <p className="text-foreground">{user.username}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Email</label>
            <p className="text-foreground">{user.email}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">User ID</label>
            <p className="text-foreground">{user.id}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Role</label>
            <div className="flex items-center gap-2">
              <p className="text-foreground">
                {user.is_admin ? 'Administrator' : 'User'}
              </p>
              {user.is_admin && (
                <span className="rounded bg-blue-100 px-2 py-0.5 text-xs text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  Admin
                </span>
              )}
            </div>
          </div>
          {/* API Key */}
          <div>
            <label className="text-sm font-medium text-muted-foreground">API Key (Bearer Token)</label>
            <div className="flex items-center gap-2">
              <code className="text-xs bg-muted p-2 rounded break-all">
                {localStorage.getItem('token')}
              </code>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Use this in your curl requests as:  
              <code> -H "Authorization: Bearer &lt;token&gt;" </code>
            </p>
          </div>          
        </div>
      </Card>

      {/* Danger Zone */}
      <Card className="p-6 border-red-200">
        <h2 className="text-lg font-semibold mb-4 text-red-700">Danger Zone</h2>
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-foreground">Delete Account</h3>
            <p className="text-sm text-muted-foreground mb-3">
              Once you delete your account, there is no going back. This will permanently delete
              your account, all transactions, all deployments, and all associated data.
            </p>
            <Button
              variant="destructive"
              onClick={() => setShowDeleteModal(true)}
            >
              Delete My Account
            </Button>
          </div>
        </div>
      </Card>

      {/* Delete Account Modal */}
      <Modal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Account"
      >
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            This will deactivate your account. You will no longer be able to access the platform.
          </p>

          <div className="bg-orange-50 border border-orange-200 rounded-md p-3">
            <p className="text-sm text-orange-700 font-medium">
              ⚠️ Important: Before proceeding, please ensure:
            </p>
            <ul className="text-sm text-orange-600 mt-1 ml-4 list-disc">
              <li>You have no active deployments</li>
              <li>You have downloaded any data you need</li>
            </ul>
            <p className="text-sm text-orange-600 mt-2">
              If you have active deployments, you will receive an error and need to deactivate them first.
            </p>
          </div>

          <div>
            <label htmlFor="confirmText" className="block text-sm font-medium text-foreground mb-1">
              Type "DELETE" to confirm:
            </label>
            <input
              id="confirmText"
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              placeholder="DELETE"
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button 
              variant="outline" 
              onClick={() => {
                setShowDeleteModal(false);
                setConfirmText('');
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAccount}
              disabled={confirmText !== 'DELETE' || loading}
            >
              {loading ? 'Deleting...' : 'Delete My Account'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AccountSettings;