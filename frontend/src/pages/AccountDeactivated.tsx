import React from 'react';
import { Card } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { useNavigate } from 'react-router-dom';

const AccountDeactivated: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card className="p-8">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Account Deactivated
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Your account has been deactivated and you cannot access the platform at this time.
            </p>
            <p className="mt-4 text-sm text-gray-600">
              If you believe this is an error or would like to discuss reactivating your account, 
              please contact our support team using the chat widget below.
            </p>
          </div>

          <div className="mt-8 space-y-4">
            <Button
              onClick={() => navigate('/login')}
              variant="outline"
              className="w-full"
            >
              Back to Login
            </Button>
            
            <div className="text-center">
              <p className="text-xs text-gray-500">
                Need help? Use the chat widget in the bottom right corner to contact support.
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AccountDeactivated;
