import React, { useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts';
import { Button } from '@/components/ui/button';

const Home: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const wistiaContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Load Wistia scripts
    const loadWistiaScripts = () => {
      // Load the main player script
      const playerScript = document.createElement('script');
      playerScript.src = 'https://fast.wistia.com/player.js';
      playerScript.async = true;
      document.head.appendChild(playerScript);

      // Load the specific video script
      const videoScript = document.createElement('script');
      videoScript.src = 'https://fast.wistia.com/embed/y7w4ax30qi.js';
      videoScript.async = true;
      videoScript.type = 'module';
      document.head.appendChild(videoScript);

      // Add the CSS styles
      const style = document.createElement('style');
      style.textContent = `
        wistia-player[media-id='y7w4ax30qi']:not(:defined) {
          background: center / contain no-repeat url('https://fast.wistia.com/embed/medias/y7w4ax30qi/swatch');
          display: block;
          filter: blur(5px);
          padding-top: 56.25%;
        }
      `;
      document.head.appendChild(style);
    };

    loadWistiaScripts();

    // Insert the Wistia player element
    if (wistiaContainerRef.current) {
      wistiaContainerRef.current.innerHTML = '<wistia-player media-id="y7w4ax30qi" aspect="1.7777777777777777"></wistia-player>';
    }

    // Cleanup function to remove scripts when component unmounts
    return () => {
      const scripts = document.querySelectorAll('script[src*="wistia"]');
      scripts.forEach(script => script.remove());

      const styles = document.querySelectorAll('style');
      styles.forEach(style => {
        if (style.textContent?.includes('wistia-player')) {
          style.remove();
        }
      });
    };
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen text-center">
      <img src="manadae.png" alt="Manidae Cloud" className="h-8 mb-4" />
      <h1 className="text-4xl font-bold tracking-tight lg:text-5xl mb-2">
        Manidae Cloud
      </h1>
      <p className="text-xl text-muted-foreground mb-8 max-w-2xl">
        Simplified managed hosting platform for deploying pre-configured open-source software packages on cloud infrastructure.
      </p>

      {/* Wistia Video */}
      <div className="mb-8 max-w-4xl w-full" ref={wistiaContainerRef}>
        {/* Wistia player will be inserted here by useEffect */}
      </div>

      {isAuthenticated ? (
        <div className="space-y-4">
          <p className="text-lg">Welcome back, {user?.username}!</p>
          <div className="space-x-4">
            <Link to="/dashboard">
              <Button>Go to Dashboard</Button>
            </Link>
            <Link to="/create-deployment">
              <Button variant="secondary">Create Deployment</Button>
            </Link>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="space-x-4">
            <Link to="/login">
              <Button>Login</Button>
            </Link>
            <Link to="/signup">
              <Button variant="secondary">Sign Up</Button>
            </Link>
          </div>
          <Link to="/pricing">
            <Button variant="outline">View Pricing</Button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default Home;
