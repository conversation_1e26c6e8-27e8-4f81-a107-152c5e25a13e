import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { apiClient } from '../api';
import { useAuth } from '../contexts';
import { Button } from '../components/ui/button';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '../components/ui/pagination';

interface Transaction {
  id: number;
  user_id: number;
  amount: number;
  type: 'billing' | 'manual_adjustment' | 'payment' | 'termination' | string;
  description?: string;
  created_at: string;
}

interface GroupedTransaction {
  date: string;
  transactions: Transaction[];
}

interface SignupCredit {
  id: number;
  amount: number;
  expires_at: string | null;
  used_amount: number;
  remaining_amount: number;
  is_active: boolean;
  is_expired: boolean;
  is_usable: boolean;
  created_at: string;
}

interface BalanceInfo {
  user_id: number;
  regular_balance: number;
  referral_credits: number;
  signup_credits: number;
  total_balance: number;
}

interface ReferralCredit {
  id: number;
  amount: number;
  credit_type: string;
  expires_at: string | null;
  used_amount: number;
  remaining_amount: number;
  is_active: boolean;
  is_expired: boolean;
  is_usable: boolean;
  created_at: string;
}

const Billing: React.FC = () => {
  const { user, refreshUser } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const filterUser = searchParams.get('user');
  const currentPage = parseInt(searchParams.get('page') || '1', 10);
  const [groupedTransactions, setGroupedTransactions] = useState<GroupedTransaction[]>([]);
  const [signupCredits, setSignupCredits] = useState<SignupCredit[]>([]);
  const [referralCredits, setReferralCredits] = useState<ReferralCredit[]>([]);
  const [balanceInfo, setBalanceInfo] = useState<BalanceInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [topUpAmount, setTopUpAmount] = useState<number>(10);
  const [paginationInfo, setPaginationInfo] = useState({
    total_pages: 1,
    current_page: 1,
    has_next: false,
    has_previous: false
  });
  const daysPerPage = 30;

  useEffect(() => {
    const load = async () => {
      try {
        setLoading(true);

        // Load transactions with pagination
        let txsResponse;
        if (filterUser && user?.is_admin) {
          // Admin viewing specific user's transactions
          txsResponse = await apiClient.getAdminTransactions(filterUser, currentPage, daysPerPage);
        } else {
          // Regular user viewing their own transactions
          txsResponse = await apiClient.getTransactions(currentPage, daysPerPage);
        }
        console.log('Raw API response:', txsResponse);

        setGroupedTransactions(txsResponse.transactions as unknown as GroupedTransaction[]);
        setPaginationInfo({
          total_pages: txsResponse.total_pages,
          current_page: txsResponse.current_page,
          has_next: txsResponse.has_next,
          has_previous: txsResponse.has_previous
        });

        // Load signup credits (only on first page for performance)
        if (currentPage === 1) {
          try {
            const signupCreds = await apiClient.getSignupCredits();
            setSignupCredits(signupCreds);
          } catch (e) {
            console.log('No signup credits or error loading:', e);
          }

          // Load referral credits (only on first page for performance)
          try {
            const referralCreds = await apiClient.getReferralCredits();
            setReferralCredits(referralCreds);
          } catch (e) {
            console.log('No referral credits or error loading:', e);
          }

          // Load balance info (only on first page for performance)
          if (user?.id) {
            try {
              const balance = await apiClient.getUserBalance(user.id);
              setBalanceInfo(balance);
            } catch (e) {
              console.log('Error loading balance info:', e);
            }
          }
        }

        await refreshUser();
      } catch (e: any) {
        setError(e?.message || 'Failed to load billing data');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [filterUser, currentPage]);

  const startStripeTopUp = async () => {
    try {
      setError(null);
      const amountCents = Math.round(topUpAmount * 100);
      const session = await apiClient.createStripeCheckoutSession(amountCents);
      if (session?.url) {
        window.location.href = session.url;
      }
    } catch (e: any) {
      setError(e?.message || 'Failed to start Stripe checkout');
    }
  };

  const handlePageChange = (page: number) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('page', page.toString());
    setSearchParams(newSearchParams);
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">
          {filterUser ? `Billing - ${filterUser}` : 'Billing'}
        </h1>
        <div className="text-sm text-muted-foreground">
          {balanceInfo ? (
            <div className="text-right">
              <div className="font-medium text-lg">
                Total: ${balanceInfo.total_balance.toFixed(2)} USD
              </div>
              <div className="text-xs space-y-1">
                <div>Regular: ${balanceInfo.regular_balance.toFixed(2)} USD</div>
                {balanceInfo.referral_credits > 0 && (
                  <div>Referral Credits: ${balanceInfo.referral_credits.toFixed(2)} USD</div>
                )}
                {balanceInfo.signup_credits > 0 && (
                  <div>Signup Credits: ${balanceInfo.signup_credits.toFixed(2)} USD</div>
                )}
              </div>
            </div>
          ) : (
            <span>Current balance: {user?.balance != null ? `$${parseFloat(String(user.balance)).toFixed(2)} USD` : '—'}</span>
          )}
        </div>
      </div>

      {/* Show notice when admin is viewing another user's billing */}
      {filterUser && user?.is_admin && (
        <div className="rounded border border-blue-200 bg-blue-50 p-3">
          <p className="text-sm text-blue-800">
            <strong>Admin View:</strong> You are viewing {filterUser}'s billing information.
            To add funds to your own account, visit your personal billing page.
          </p>
        </div>
      )}

      {/* Only show Add funds section when viewing own billing (not another user's) */}
      {!filterUser && (
        <div className="rounded border p-4 space-y-3">
          <h2 className="font-medium">Add funds</h2>
          <div className="flex items-center gap-3">
            <input
              type="number"
              min={1}
              step={1}
              value={topUpAmount}
              onChange={(e) => setTopUpAmount(parseFloat(e.target.value))}
              className="w-28 rounded border bg-background px-2 py-1"
            />
            <span>USD</span>
            <Button onClick={startStripeTopUp}>Add funds with Stripe</Button>
          </div>
          <p className="text-xs text-muted-foreground">
            You will be redirected to Stripe to complete the payment securely.
          </p>
          {error && <div className="text-sm text-red-600">{error}</div>}
        </div>
      )}

      {/* Credits Section */}
      {(signupCredits.length > 0 || referralCredits.length > 0) && (
        <div className="rounded border p-4">
          <h2 className="font-medium mb-3">Account Credits</h2>
          <div className="space-y-4">

            {/* Signup Credits */}
            {signupCredits.length > 0 && (
              <div>
                <h3 className="text-sm font-semibold text-muted-foreground mb-2">Welcome Credits</h3>
                <div className="space-y-2">
                  {signupCredits.map((credit) => (
                    <div key={credit.id} className="flex items-center justify-between text-sm ml-2">
                      <div>
                        <div className="font-medium">Welcome Signup Credit</div>
                        <div className="text-muted-foreground text-xs">
                          {credit.expires_at
                            ? `Expires: ${new Date(credit.expires_at).toLocaleDateString()}`
                            : 'No expiry'
                          }
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-medium ${credit.is_usable ? 'text-green-600' : credit.is_expired ? 'text-red-600' : 'text-gray-600'}`}>
                          ${credit.remaining_amount.toFixed(2)} / ${credit.amount.toFixed(2)}
                        </div>
                        <div className="text-muted-foreground text-xs">
                          {credit.is_expired ? 'Expired' : credit.is_usable ? 'Active' : 'Inactive'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Referral Credits */}
            {referralCredits.length > 0 && (
              <div>
                <h3 className="text-sm font-semibold text-muted-foreground mb-2">Referral Credits</h3>
                <div className="space-y-2">
                  {referralCredits.map((credit) => (
                    <div key={credit.id} className="flex items-center justify-between text-sm ml-2">
                      <div>
                        <div className="font-medium">
                          {credit.credit_type === 'referrer' ? 'Referrer Reward' : 'Referral Bonus'}
                        </div>
                        <div className="text-muted-foreground text-xs">
                          {credit.expires_at
                            ? `Expires: ${new Date(credit.expires_at).toLocaleDateString()}`
                            : 'No expiry'
                          }
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-medium ${credit.is_usable ? 'text-green-600' : credit.is_expired ? 'text-red-600' : 'text-gray-600'}`}>
                          ${credit.remaining_amount.toFixed(2)} / ${credit.amount.toFixed(2)}
                        </div>
                        <div className="text-muted-foreground text-xs">
                          {credit.is_expired ? 'Expired' : credit.is_usable ? 'Active' : 'Inactive'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="rounded border p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="font-medium">Transactions</h2>
          <div className="text-sm text-muted-foreground">
            Showing {daysPerPage} days per page
          </div>
        </div>
        {loading ? (
          <div className="text-sm text-muted-foreground">Loading…</div>
        ) : (
          <div className="space-y-4">
            {groupedTransactions.length === 0 ? (
              <div className="text-sm text-muted-foreground">No transactions yet.</div>
            ) : (
              groupedTransactions.map((group) => (
                <div key={group.date}>
                  <h3 className="text-sm font-semibold text-muted-foreground mb-2 border-b pb-2">{formatDate(group.date)}</h3>
                  <div className="space-y-2">
                    {group.transactions.map((tx) => (
                      <div key={tx.id} className="flex items-center justify-between text-sm ml-2">
                        <div>
                          <div className="font-medium capitalize">{(tx.type || 'unknown').replace('_', ' ')}</div>
                          <div className="text-muted-foreground text-xs">{tx.description || ''}</div>
                        </div>
                        <div className="text-right">
                           <div className={`font-medium ${parseFloat(String(tx.amount)) >= 0 ? 'text-green-600' : ''}`}>
                            {parseFloat(String(tx.amount)) >= 0 ? '+' : ''}${parseFloat(String(tx.amount)).toFixed(2)} USD
                          </div>
                          <div className="text-muted-foreground text-xs">{new Date(tx.created_at).toLocaleTimeString()}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}

            {/* Pagination Controls */}
            {paginationInfo.total_pages > 1 && (
              <div className="mt-6 flex justify-center">
                <Pagination>
                  <PaginationContent>
                    {paginationInfo.has_previous && (
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePageChange(currentPage - 1)}
                          className="cursor-pointer"
                        />
                      </PaginationItem>
                    )}

                    {/* Show page numbers */}
                    {Array.from({ length: Math.min(5, paginationInfo.total_pages) }, (_, i) => {
                      let pageNum: number;
                      if (paginationInfo.total_pages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= paginationInfo.total_pages - 2) {
                        pageNum = paginationInfo.total_pages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink
                            onClick={() => handlePageChange(pageNum)}
                            isActive={pageNum === currentPage}
                            className="cursor-pointer"
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}

                    {paginationInfo.has_next && (
                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePageChange(currentPage + 1)}
                          className="cursor-pointer"
                        />
                      </PaginationItem>
                    )}
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Billing;

