import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { apiClient } from '@/api';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts';

const VerifyEmail: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { refreshUser } = useAuth();
  const [status, setStatus] = useState<'pending' | 'success' | 'error'>('pending');
  const [message, setMessage] = useState<string>('Verifying your email...');

  useEffect(() => {
    const token = searchParams.get('token');
    if (!token) {
      setStatus('error');
      setMessage('Missing verification token');
      return;
    }
    (async () => {
      try {
        const res = await apiClient.verifyEmailToken(token);
        setStatus('success');
        setMessage(res.message || 'Email verified successfully');
        // Refresh user data to update verification status in auth context
        await refreshUser();
      } catch (e: any) {
        setStatus('error');
        setMessage(e?.message || 'Verification failed');
      }
    })();
  }, [searchParams, refreshUser]);

  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="border rounded-lg p-8 max-w-md w-full bg-card text-foreground shadow-sm text-center">
        {status === 'pending' && (
          <>
            <h1 className="text-xl font-semibold mb-2">Verifying...</h1>
            <p className="text-muted-foreground">{message}</p>
          </>
        )}
        {status === 'success' && (
          <>
            <h1 className="text-xl font-semibold mb-2">Email Verified</h1>
            <p className="text-muted-foreground mb-4">{message}</p>
            <Button onClick={() => navigate('/dashboard')}>Go to Dashboard</Button>
          </>
        )}
        {status === 'error' && (
          <>
            <h1 className="text-xl font-semibold mb-2">Verification Failed</h1>
            <p className="text-muted-foreground mb-4">{message}</p>
            <Button variant="outline" onClick={() => navigate('/login')}>Back to Login</Button>
          </>
        )}
      </div>
    </div>
  );
};

export default VerifyEmail;

