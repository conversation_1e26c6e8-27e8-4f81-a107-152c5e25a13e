import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle } from 'lucide-react';

const PaymentSuccess: React.FC = () => {
  const navigate = useNavigate();
  const { refreshUser } = useAuth();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  
  const sessionId = searchParams.get('session_id');
  const testMode = searchParams.get('test_mode') === 'true';

  useEffect(() => {
    // Refresh user data to get updated balance
    const refreshData = async () => {
      try {
        await refreshUser();
      } catch (error) {
        console.error('Failed to refresh user data:', error);
      } finally {
        setLoading(false);
      }
    };

    // Add a small delay to ensure webhook has processed
    // Shorter delay for test mode since it's processed immediately
    const delay = testMode ? 500 : 2000;
    const timer = setTimeout(refreshData, delay);
    return () => clearTimeout(timer);
  }, [refreshUser, testMode]);

  const handleGoToBilling = () => {
    navigate('/billing');
  };

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl text-green-600">
              {testMode ? 'Test Payment Successful!' : 'Payment Successful!'}
            </CardTitle>
            <CardDescription>
              {testMode
                ? 'Your test payment has been processed and your account has been credited.'
                : 'Your payment has been processed successfully and your account has been credited.'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {sessionId && (
              <div className="text-sm text-muted-foreground">
                <p>Session ID: {sessionId}</p>
                {testMode && <p className="text-orange-600 font-medium">🧪 Test Mode</p>}
              </div>
            )}
            
            {loading && (
              <div className="text-center text-sm text-muted-foreground">
                Updating your account balance...
              </div>
            )}
            
            <div className="space-y-2">
              <Button onClick={handleGoToBilling} className="w-full">
                View Billing & Transactions
              </Button>
              <Button onClick={handleGoToDashboard} variant="outline" className="w-full">
                Go to Dashboard
              </Button>
            </div>
            
            <div className="text-xs text-muted-foreground text-center">
              It may take a few moments for your balance to update. If you don't see the credit immediately, please refresh the page.
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PaymentSuccess;
