import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts';
import { apiClient } from '../api';
import { toast } from '@/components/ui/toast';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { isReferralProgramEnabled } from '../utils/referrals';

const SignUp: React.FC = () => {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [referralCode, setReferralCode] = useState('');
  const [referrerUsername, setReferrerUsername] = useState('');
  const [validationError, setValidationError] = useState('');
  const { signup, isLoading, error, isAuthenticated, clearError } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Check for referral code in URL
  useEffect(() => {
    const refCode = searchParams.get('ref');
    if (refCode) {
      setReferralCode(refCode);
    }
  }, [searchParams]);

  const handleReferralCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const code = e.target.value;
    setReferralCode(code);
  };


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setValidationError('');

    if (password !== confirmPassword) {
      setValidationError('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setValidationError('Password must be at least 6 characters long');
      return;
    }

    // Validate referral code if provided (for toast notification only)
    let referralCodeValid = true;
    if (referralCode.trim()) {
      try {
        const result = await apiClient.validateReferralCode(referralCode.trim());
        setReferrerUsername(result.referrer_username);
      } catch (error) {
        referralCodeValid = false;
        toast('Invalid referral code - signup will proceed without referral benefits', 'error');
      }
    }

    try {
      await signup({
        username,
        email,
        password,
        referral_code: referralCode.trim() || undefined
      });

      // Show success message with referral info
      if (referralCode.trim() && referralCodeValid) {
        toast(`Account created successfully! You and ${referrerUsername} will receive referral credits.`, 'success');
      } else {
        toast('Account created successfully!', 'success');
      }

      // User is automatically logged in after successful signup
      // The useEffect hook will redirect to dashboard
    } catch (error) {
      // Error is handled by context
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl">Sign Up</CardTitle>
          <CardDescription>
            Create an account to deploy your first application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {(error || validationError) && (
            <div className="p-3 text-red-700 bg-red-100 border border-red-400 rounded text-sm">
              {error || validationError}
            </div>
          )}
          <form onSubmit={handleSubmit} id="signup-form" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
                minLength={6}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-password">Confirm Password</Label>
              <Input
                id="confirm-password"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                disabled={isLoading}
                minLength={6}
              />
            </div>
            {isReferralProgramEnabled() && (
              <div className="space-y-2">
                <Label htmlFor="referral-code">
                  Referral Code <span className="text-muted-foreground">(optional)</span>
                </Label>
                <Input
                  id="referral-code"
                  type="text"
                  value={referralCode}
                  onChange={handleReferralCodeChange}
                  disabled={isLoading}
                  placeholder="Enter referral code (optional)"

                />
                {referralCode.trim() && (
                  <div className="text-sm text-blue-600">
                    🎁 Referral code will be validated during signup. You and the referrer will receive credits if valid!
                  </div>
                )}
              </div>
            )}
          </form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <Button
            type="submit"
            form="signup-form"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? 'Creating Account...' : 'Sign Up'}
          </Button>
          <p className="text-sm text-center">
            Already have an account?{' '}
            <Link to="/login" className="text-primary hover:underline">
              Login
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SignUp;
