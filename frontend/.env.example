# Deployment Creation Mode
# Set to 'false' to enable REAL deployment creation (⚠️ WILL INCUR COSTS!)
# Set to 'true' or leave unset to mock deployment creation (safe for development)
VITE_MOCK_DEPLOYMENT_CREATION=true

# Deployment System Variables
VITE_POSTGRES_HOST=pangolin-postgres
VITE_POSTGRES_USER=REPLACE_USER
VITE_POSTGRES_PASSWORD=REPLACE_PASSWORD
VITE_GITHUB_REPO=oidebrett/manidae

VITE_API_SECRET_KEY=REPLACE_API_SECRET_KEY

# Pricing Configuration
VITE_MIN_BALANCE_TO_DEPLOY=4.99
VITE_REFERRAL_CREDIT_AMOUNT=5.00
VITE_REFERRER_CREDIT_AMOUNT=5.00
