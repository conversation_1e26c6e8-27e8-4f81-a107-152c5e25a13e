# Manidae Cloud - First Time Setup Guide

Welcome to Manidae Cloud! This guide will walk you through setting up your instance for the first time.

## Prerequisites

Before you begin, ensure that:
- The backend server is running
- The database is initialized (see backend README)
- You have the frontend application running

## Setup Process

### Step 1: Start the Services

1. **Backend**: Make sure the backend API is running on `http://localhost:8000`
2. **Frontend**: Start the frontend with `npm start` (runs on `http://localhost:3000`)
3. **Database**: Ensure PostgreSQL is running with the initialized schema

### Step 2: Create Your Administrator Account

1. Navigate to the frontend application in your browser
2. Click **"Sign Up"** to create a new account
3. Fill in your details:
   - **Username**: Your preferred username
   - **Email**: Your email address  
   - **Password**: A secure password

**Important**: The very first user to register will automatically become an administrator!

### Step 3: Complete Setup

After registering and logging in as the first user:

1. You'll see a welcome modal explaining your administrator privileges
2. The modal will remind you to run the database initialization script if you haven't already:
   ```bash
   python -m app.db.init_db
   ```
3. Click "Got it, let's get started!" to dismiss the modal

### Step 4: Explore Administrator Features

As an administrator, you now have access to:

- **Dashboard** - View all deployments from all users
- **Users** - Manage user accounts, create new users, assign admin roles
- **Account** - Manage your own account settings
- **Create Deployment** - Deploy applications to cloud providers

## User Management

### Creating Additional Users

As an administrator, you can create additional users:

1. Navigate to **Users** in the top navigation
2. Click **"Create New User"**
3. Fill in the user details
4. Toggle **"Administrator"** if you want them to have admin privileges
5. Click **"Create User"**

### User Roles

- **Administrator**:
  - Can view and manage all deployments
  - Can create, delete, and manage all user accounts
  - Can promote users to administrator status
  - Can access system-wide settings

- **Regular User**:
  - Can only view and manage their own deployments
  - Can manage their own account settings
  - Can delete their own account (which also deletes their deployments)

## Security Notes

- **No Default Accounts**: The system doesn't create any default users with preset passwords
- **First User Privilege**: Only the first user to register gets automatic admin privileges
- **Secure Setup**: Change your password after initial setup if needed
- **Admin Responsibilities**: As an admin, you're responsible for managing user access

## Troubleshooting

### "No administrators found" scenario
If somehow all administrators are removed from the system:
1. The next user to log in will see instructions
2. Run the database initialization script to restore admin access
3. Contact system administrator if you can't access the backend

### Database Issues
If you encounter database-related errors:
1. Ensure PostgreSQL is running
2. Verify your `.env` file has correct database credentials
3. Run `python -m app.db.init_db` to reset the database schema

### API Connection Issues
If the frontend can't connect to the backend:
1. Verify the backend is running on `http://localhost:8000`
2. Check that the `VITE_API_SECRET_KEY` matches your backend configuration
3. Ensure CORS is properly configured in the backend

## Next Steps

After setup is complete:
1. Create your first cloud deployment
2. Invite team members by creating user accounts
3. Explore the various cloud provider integrations
4. Set up your preferred cloud provider credentials

Enjoy using Manidae Cloud! 🎉