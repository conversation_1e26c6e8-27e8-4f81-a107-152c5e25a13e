# Managed Hosting Platform - Software Specification

## 1. Project Overview

### 1.1 Purpose
A simplified managed hosting platform that allows users to deploy pre-configured open-source software packages on cloud infrastructure with monthly billing based on infrastructure costs and support levels.

### 1.2 Core Value Proposition
- Simplified deployment of curated open-source stack combinations
- Transparent pricing with real-time cost calculation
- Automated infrastructure provisioning via Terraform
- Tiered support options

### 1.3 Target Users
- Small to medium businesses needing managed infrastructure
- Developers looking for quick deployment solutions
- Organizations requiring specific open-source stack combinations

## 2. Package Definitions

### 2.1 Basic Package
- **Components**: Pangolin + Middleware Manager
- **Database**: PostgreSQL
- **Target Use Case**: Basic proxy and middleware management

### 2.2 Mid Package  
- **Components**: Pangolin + Middleware Manager + CrowdSec
- **Database**: PostgreSQL
- **Target Use Case**: Enhanced security with intrusion detection

### 2.3 Deluxe Package
- **Components**: Pangolin + Middleware Manager + CrowdSec + Komodo + Static Page
- **Database**: PostgreSQL
- **Target Use Case**: Full-featured deployment with container management and static hosting

## 3. Technical Architecture

### 3.1 Backend Infrastructure
- **Terraform Integration**: Direct integration with your existing komodo-provider
- **Komodo Server**: Central management server (http://your-komodo-api-endpoint:9120)
- **Cloud Providers**: Google Cloud Platform (with expansion capability for AWS, Azure)
- **Configuration Management**: Template-based configuration using TOML templates for ResourceSync
- **GitHub Integration**: Automatic repository creation for client configurations
- **Database**: Application database for user management, billing, and deployment tracking

### 3.2 Frontend Architecture
- **Technology Stack**: React.js with TypeScript
- **Styling**: Tailwind CSS for consistent, modern UI
- **State Management**: React Context or Redux for complex state
- **API Communication**: RESTful API with proper error handling

### 3.3 Backend API
- **Framework**: Node.js with Express or Python with FastAPI
- **Database**: PostgreSQL for application data
- **Authentication**: JWT-based authentication
- **Payment Processing**: Stripe integration
- **Infrastructure Management**: Terraform execution engine

## 4. User Flow & Interface Requirements

### 4.1 Step 1: Package Selection
**UI Requirements:**
- Clean grid layout showing three package options
- Each package card displays:
  - Package name and description
  - Included components with icons
  - "Select" button
- Visual indicators for package complexity/features
- Responsive design for mobile/desktop

**Functionality:**
- Package selection updates pricing calculator
- Clear progression indicator (Step 1 of 4)

### 4.2 Step 2: Cloud Provider & Instance Selection
**UI Requirements:**
- Cloud provider selection (Google Cloud initially)
- Region selection with geographical map or dropdown
- Instance type selection with specifications:
  - CPU count
  - RAM amount
  - Storage capacity
  - Bandwidth allowance
- Real-time pricing updates in sidebar
- Performance recommendations based on package

**Functionality:**
- Dynamic pricing calculation based on selections
- Instance availability checking
- Regional pricing variations
- Performance guidance tooltips

### 4.3 Step 3: Support Level Selection
**UI Requirements:**
- Three support tiers displayed as cards:
  - **Basic**: Community support, documentation access
  - **Standard**: Email support, 24h response time, monitoring
  - **Premium**: Priority support, phone support, dedicated success manager
- Feature comparison table
- Pricing clearly displayed for each tier
- Recommended tier highlighting

**Functionality:**
- Support tier selection updates total pricing
- Clear feature differentiation
- Upgrade/downgrade information

### 4.4 Step 4: Configuration & Payment
**UI Requirements:**
- Deployment configuration form:
  - Domain name input
  - Admin credentials setup
  - Optional OAuth configuration
  - GitHub repository connection (for Deluxe)
- Pricing summary with breakdown
- Payment form integration
- Terms of service acceptance

**Functionality:**
- Form validation and error handling
- Secure payment processing
- Configuration validation
- Deployment initiation

## 5. Core Features & Functionality

### 5.1 Real-time Pricing Calculator
- Dynamic cost calculation based on:
  - Infrastructure costs (instance type, region)
  - Support tier pricing
  - Package complexity multipliers
- Monthly cost projection
- Usage-based billing estimates
- Currency localization support

### 5.2 Deployment Engine
- **Terraform Configuration Generation**: Auto-generate main.tf and terraform.tfvars from user selections
- **Komodo Integration**: 
  - Creates komodo-provider_user resource with templated TOML configuration
  - Automatically creates GitHub repository for client sync resources
  - Manages ResourceSync lifecycle (Setup → Sync → Deploy)
- **Cloud VM Provisioning**: Creates cloud instance with Docker and periphery agent
- **Automated Deployment Execution**: 
  - Runs terraform plan/apply
  - Executes ProcedureApply for stack deployment
  - Progress tracking through Komodo API
- **Rollback Capability**: ProcedureDestroy for clean teardown

### 5.3 User Dashboard
- Active deployments overview
- Resource usage monitoring
- Billing history and upcoming charges
- Support ticket management
- Configuration management interface

### 5.4 Billing & Payment System
- Monthly subscription billing
- Usage-based infrastructure charges
- Payment method management
- Invoice generation and history
- Automatic payment processing
- Usage alerts and notifications

## 6. Database Schema

### 6.1 Core Tables
```
Users
- id, email, password_hash, created_at, updated_at
- billing_address, payment_method_id

Deployments
- id, user_id, package_type, status, created_at
- terraform_state, configuration_json
- cloud_provider, region, instance_type
- monthly_cost, support_tier

Billing
- id, user_id, deployment_id, amount, billing_period
- status, payment_date, invoice_url

Support_Tickets
- id, user_id, deployment_id, priority, status
- subject, description, created_at, updated_at
```

### 6.2 Configuration Storage
- Encrypted storage for sensitive configuration data
- Version control for configuration changes
- Backup and restore capabilities

## 7. API Endpoints

### 7.1 Package Management
```
GET /api/packages - List available packages
GET /api/packages/:id - Get package details
```

### 7.2 Infrastructure
```
GET /api/cloud-providers - List supported providers
GET /api/regions/:provider - List regions for provider
GET /api/instance-types/:provider/:region - List available instances
POST /api/pricing/calculate - Calculate pricing for configuration
```

### 7.3 Deployment
```
POST /api/deployments - Create new deployment
  Body: {
    package_type: "basic|mid|deluxe",
    cloud_provider: "gcp",
    region: "us-central1",
    zone: "us-central1-a",
    machine_type: "e2-medium",
    client_name: "string",
    domain: "string",
    admin_email: "string",
    admin_username: "string",
    admin_password: "string",
    admin_subdomain: "string",
    support_tier: "basic|standard|premium"
  }
GET /api/deployments - List user deployments
GET /api/deployments/:id - Get deployment details
PUT /api/deployments/:id - Update deployment configuration
DELETE /api/deployments/:id - Destroy deployment (runs ProcedureDestroy)
GET /api/deployments/:id/status - Get deployment status from Komodo
GET /api/deployments/:id/logs - Get deployment logs
POST /api/deployments/:id/procedures/:procedure_name/execute - Execute specific procedure
```

### 7.4 Billing
```
GET /api/billing/history - Get billing history
POST /api/billing/payment-methods - Add payment method
GET /api/billing/usage/:deployment_id - Get usage statistics
```

## 8. Security Requirements

### 8.1 Authentication & Authorization
- JWT-based authentication
- Role-based access control
- Multi-factor authentication support
- Session management and timeout

### 8.2 Data Protection
- Encryption at rest for sensitive data
- TLS encryption for all API communications
- Secure credential storage and rotation
- PCI DSS compliance for payment processing

### 8.3 Infrastructure Security
- Terraform state file encryption
- Cloud provider credential management
- Network security group configuration
- Regular security updates and patches

## 9. Integration Requirements

### 9.1 Terraform Integration
- Terraform Cloud/Enterprise compatibility
- State file management and locking
- Variable interpolation and validation
- Plan/apply/destroy operations

### 9.2 Payment Processing
- Stripe integration for payment processing
- Webhook handling for payment events
- Subscription management
- Invoice generation and delivery

### 9.3 Monitoring & Alerting
- Infrastructure monitoring integration
- Cost alerting and budget management
- Performance monitoring
- Error tracking and logging

## 10. Performance Requirements

### 10.1 Response Times
- Page load times: < 2 seconds
- API response times: < 500ms
- Deployment initiation: < 30 seconds
- Real-time pricing updates: < 200ms

### 10.2 Scalability
- Support for 1000+ concurrent users
- Horizontal scaling capability
- Database connection pooling
- CDN integration for static assets

## 11. Development Phases

### 11.1 Phase 1: MVP (4-6 weeks)
- Basic package selection interface
- Google Cloud integration only
- Single support tier
- Basic billing system
- Core deployment functionality

### 11.2 Phase 2: Enhanced Features (3-4 weeks)
- Multiple support tiers
- Advanced pricing calculator
- User dashboard
- Monitoring integration

### 11.3 Phase 3: Scale & Polish (2-3 weeks)
- Additional cloud providers
- Advanced configuration options
- Performance optimization
- Enhanced security features

## 12. Testing Requirements

### 12.1 Unit Testing
- 80%+ code coverage
- API endpoint testing
- Business logic validation
- Error handling verification

### 12.2 Integration Testing
- Terraform deployment testing
- Payment processing testing
- Third-party service integration
- End-to-end user flows

### 12.3 Load Testing
- Concurrent user simulation
- Database performance testing
- API rate limiting validation
- Infrastructure scaling tests

## 13. Deployment & Operations

### 13.1 Application Deployment
- Containerized deployment (Docker)
- CI/CD pipeline setup
- Environment management (dev/staging/prod)
- Database migration management

### 13.2 Monitoring & Logging
- Application performance monitoring
- Error tracking and alerting
- User activity analytics
- Infrastructure cost monitoring

### 13.3 Backup & Recovery
- Regular database backups
- Configuration backup
- Disaster recovery procedures
- Data retention policies

## 14. Success Metrics

### 14.1 Business Metrics
- Monthly recurring revenue (MRR)
- Customer acquisition cost (CAC)
- Customer lifetime value (CLV)
- Churn rate

### 14.2 Technical Metrics
- Deployment success rate (>95%)
- Average deployment time (<10 minutes)
- System uptime (99.9%)
- API response time (<500ms)

### 14.3 User Experience Metrics
- Time to first deployment
- User onboarding completion rate
- Support ticket resolution time
- Customer satisfaction score

## 15. Package-Specific TOML Configuration Templates

### 15.1 Basic Package Template (Pangolin + Middleware Manager)
```toml
[[server]]
name = "server-${client_name_lower}"

[[stack]]
name = "${client_name_lower}_pangolin_stack"
[stack.config]
server = "server-${client_name_lower}"
file_contents = """
services:
  postgres:
    image: postgres:15
    container_name: ${client_name_lower}_postgres
    environment:
      POSTGRES_DB: ${postgres_db}
      POSTGRES_USER: ${postgres_user}
      POSTGRES_PASSWORD: ${postgres_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    
  pangolin:
    image: pangolin:latest
    container_name: ${client_name_lower}_pangolin
    depends_on:
      - postgres
    environment:
      DATABASE_URL: postgresql://${postgres_user}:${postgres_password}@postgres:5432/${postgres_db}
      DOMAIN: ${domain}
      ADMIN_EMAIL: ${admin_email}
    ports:
      - "80:80"
      - "443:443"
    restart: unless-stopped
    
  middleware-manager:
    image: middleware-manager:latest
    container_name: ${client_name_lower}_middleware
    depends_on:
      - postgres
    environment:
      DATABASE_URL: postgresql://${postgres_user}:${postgres_password}@postgres:5432/${postgres_db}
      ADMIN_USERNAME: ${admin_username}
      ADMIN_PASSWORD: ${admin_password}
    ports:
      - "8080:8080"
    restart: unless-stopped

volumes:
  postgres_data:
"""

[[procedure]]
name = "${client_name}_ProcedureApply"
description = "Deploy Basic Package (Pangolin + Middleware Manager)"
[[procedure.config.stage]]
name = "${client_name}_Stack"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "${client_name_lower}_pangolin_stack", execution.params.services = [], enabled = true }
]

[[procedure]]
name = "${client_name}_ProcedureDestroy"
description = "Destroy Basic Package deployment"
[[procedure.config.stage]]
name = "${client_name}_Stack"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "${client_name_lower}_pangolin_stack", execution.params.services = [], execution.params.remove_orphans = true, enabled = true }
]
```

### 15.2 Mid Package Template (+ CrowdSec)
```toml
# Extends Basic package with CrowdSec addition
  crowdsec:
    image: crowdsecurity/crowdsec:latest
    container_name: ${client_name_lower}_crowdsec
    environment:
      COLLECTIONS: "crowdsecurity/nginx crowdsecurity/base-http-scenarios"
      ENROLLMENT_KEY: ${crowdsec_enrollment_key}
    volumes:
      - /var/log:/var/log:ro
      - crowdsec_data:/var/lib/crowdsec/data
    restart: unless-stopped
```

### 15.3 Deluxe Package Template (+ Komodo + Static Page)
```toml
# Extends Mid package with Komodo and Static Page
  komodo:
    image: komodo:latest
    container_name: ${client_name_lower}_komodo
    ports:
      - "9120:9120"
    environment:
      API_KEY: ${komodo_api_key}
      API_SECRET: ${komodo_api_secret}
    restart: unless-stopped
    
  static-page:
    image: nginx:alpine
    container_name: ${client_name_lower}_static
    volumes:
      - ./static-content:/usr/share/nginx/html:ro
    ports:
      - "8000:80"
    restart: unless-stopped
```

## 16. Komodo Provider Integration Details

### 16.1 Authentication Configuration
```javascript
// Backend service configuration
const komodoConfig = {
  endpoint: process.env.KOMODO_API_ENDPOINT || "http://komodo-server:9120",
  api_key: process.env.KOMODO_API_KEY,
  api_secret: process.env.KOMODO_API_SECRET,
  github_token: process.env.GITHUB_TOKEN
};
```

### 16.2 Terraform Variable Generation
```javascript
// Generate terraform.tfvars based on user selections
function generateTerraformVars(userConfig) {
  return `
# Cloud Provider Configuration
gcp_credentials_file = "${userConfig.gcp_credentials_file}"
gcp_project_id = "${userConfig.gcp_project_id}"
gcp_region = "${userConfig.gcp_region}"
gcp_zone = "${userConfig.gcp_zone}"

# Instance Configuration
instance_name = "${userConfig.client_name.toLowerCase()}-instance"
machine_type = "${userConfig.machine_type}"
instance_image = "ubuntu-2204-jammy-v20240319"

# Networking
firewall_name = "${userConfig.client_name.toLowerCase()}-firewall"
allowed_ports = ["22", "80", "443", "8080", "9120"]
firewall_source_ranges = ["0.0.0.0/0"]
instance_tags = ["${userConfig.client_name.toLowerCase()}-server"]

# Client Configuration
client_id = "${userConfig.user_id}"
client_name = "${userConfig.client_name}"
domain = "${userConfig.domain}"
admin_email = "${userConfig.admin_email}"
admin_username = "${userConfig.admin_username}"
admin_password = "${userConfig.admin_password}"
admin_subdomain = "${userConfig.admin_subdomain}"

# Package-specific variables
postgres_user = "${userConfig.postgres_user}"
postgres_password = "${userConfig.postgres_password}"
postgres_host = "postgres"
${userConfig.package_type === 'mid' || userConfig.package_type === 'deluxe' ? 
  `crowdsec_enrollment_key = "${userConfig.crowdsec_enrollment_key}"` : ''}
${userConfig.package_type === 'deluxe' ? 
  `static_page_domain = "${userConfig.static_page_domain}"
github_repo = "${userConfig.github_repo}"` : ''}

# Komodo Provider Configuration
komodo_provider_endpoint = "${komodoConfig.endpoint}"
komodo_api_key = "${komodoConfig.api_key}"
komodo_api_secret = "${komodoConfig.api_secret}"
github_token = "${komodoConfig.github_token}"
`;
}
```

### 16.3 Deployment Status Monitoring
```javascript
// Monitor deployment through Komodo API
async function getDeploymentStatus(deploymentId) {
  const deployment = await getDeploymentFromDB(deploymentId);
  
  // Check Terraform state
  const terraformStatus = await checkTerraformState(deployment.terraform_state_path);
  
  // Check Komodo ResourceSync status
  const komodoStatus = await komodoClient.getResourceSyncStatus(deployment.client_id);
  
  // Check procedure execution status
  const procedureStatus = await komodoClient.getProcedureStatus(
    `${deployment.client_name}_ProcedureApply`
  );
  
  return {
    overall_status: determineOverallStatus(terraformStatus, komodoStatus, procedureStatus),
    terraform_status: terraformStatus,
    komodo_sync_status: komodoStatus,
    procedure_status: procedureStatus,
    server_ip: deployment.server_ip,
    services_health: await checkServicesHealth(deployment.server_ip)
  };
}
```