{"openapi": "3.1.0", "info": {"title": "Manidae Cloud API", "description": "API for managing deployments and users on the Manidae Cloud platform.", "version": "0.1.0"}, "paths": {"/api/users/": {"get": {"tags": ["Users"], "summary": "Read Users", "operationId": "read_users_api_users__get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}, "title": "Response Read Users Api Users  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Users"], "summary": "Register", "operationId": "register_api_users__post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/users/{user_id}": {"get": {"tags": ["Users"], "summary": "Read User", "operationId": "read_user_api_users__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Users"], "summary": "Delete User Endpoint", "operationId": "delete_user_endpoint_api_users__user_id__delete", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packages/": {"get": {"tags": ["Deployments"], "summary": "Get Packages", "operationId": "get_packages_api_packages__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/cloud-providers/": {"get": {"tags": ["Deployments"], "summary": "Get Cloud Providers", "operationId": "get_cloud_providers_api_cloud_providers__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/regions/{provider}": {"get": {"tags": ["Deployments"], "summary": "Get Regions", "operationId": "get_regions_api_regions__provider__get", "parameters": [{"name": "provider", "in": "path", "required": true, "schema": {"type": "string", "title": "Provider"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/instance-types/{provider}/{region}": {"get": {"tags": ["Deployments"], "summary": "Get Instance Types", "operationId": "get_instance_types_api_instance_types__provider___region__get", "parameters": [{"name": "provider", "in": "path", "required": true, "schema": {"type": "string", "title": "Provider"}}, {"name": "region", "in": "path", "required": true, "schema": {"type": "string", "title": "Region"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/pricing/calculate": {"post": {"tags": ["Deployments"], "summary": "Calculate Price", "operationId": "calculate_price_api_pricing_calculate_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentBase"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/": {"get": {"tags": ["Deployments"], "summary": "Get Deployments", "operationId": "get_deployments_api_deployments__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Deployment"}, "type": "array", "title": "Response Get Deployments Api Deployments  Get"}}}}}}, "post": {"tags": ["Deployments"], "summary": "Create Deployment", "operationId": "create_deployment_api_deployments__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Deployment"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/{deployment_id}": {"delete": {"tags": ["Deployments"], "summary": "Delete Deployment", "operationId": "delete_deployment_api_deployments__deployment_id__delete", "parameters": [{"name": "deployment_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Deployment Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Deployment"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Deployments"], "summary": "Update Deployment", "operationId": "update_deployment_api_deployments__deployment_id__patch", "parameters": [{"name": "deployment_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Deployment Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeploymentUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Deployment"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/deployments/user/{user_id}": {"get": {"tags": ["Deployments"], "summary": "Get Deployments By User", "operationId": "get_deployments_by_user_api_deployments_user__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Deployment"}, "title": "Response Get Deployments By User Api Deployments User  User Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "<PERSON> Root", "operationId": "read_root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"Deployment": {"properties": {"package": {"type": "string", "enum": ["Pangolin", "Premium"], "title": "Package"}, "cloud_provider": {"type": "string", "title": "Cloud Provider"}, "region": {"type": "string", "title": "Region"}, "instance_type": {"type": "string", "title": "Instance Type"}, "support_level": {"type": "string", "title": "Support Level"}, "komodo_provider_endpoint": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Komodo Provider Endpoint"}, "komodo_api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ko<PERSON><PERSON>"}, "komodo_api_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "github_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Name"}, "domain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Domain"}, "admin_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "admin_username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "admin_password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Admin Password"}, "admin_subdomain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Admin Subdomain"}, "postgres_user": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres User"}, "postgres_password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres Password"}, "postgres_host": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres Host"}, "github_repo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "crowdsec_enrollment_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Crowdsec Enrollment Key"}, "static_page_domain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Static Page Domain"}, "oauth_client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Oauth Client Id"}, "oauth_client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>auth Client Secret"}, "komodo_host_ip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Komodo Host Ip"}, "firewall_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firewall Name"}, "instance_tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Instance Tags"}, "id": {"type": "integer", "title": "Id"}, "user_id": {"type": "integer", "title": "User Id"}, "cost": {"type": "integer", "title": "Cost"}, "deleted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted At"}}, "type": "object", "required": ["package", "cloud_provider", "region", "instance_type", "support_level", "id", "user_id", "cost"], "title": "Deployment"}, "DeploymentBase": {"properties": {"package": {"type": "string", "enum": ["Pangolin", "Premium"], "title": "Package"}, "cloud_provider": {"type": "string", "title": "Cloud Provider"}, "region": {"type": "string", "title": "Region"}, "instance_type": {"type": "string", "title": "Instance Type"}, "support_level": {"type": "string", "title": "Support Level"}, "komodo_provider_endpoint": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Komodo Provider Endpoint"}, "komodo_api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ko<PERSON><PERSON>"}, "komodo_api_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "github_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Name"}, "domain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Domain"}, "admin_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "admin_username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "admin_password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Admin Password"}, "admin_subdomain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Admin Subdomain"}, "postgres_user": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres User"}, "postgres_password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres Password"}, "postgres_host": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres Host"}, "github_repo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "crowdsec_enrollment_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Crowdsec Enrollment Key"}, "static_page_domain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Static Page Domain"}, "oauth_client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Oauth Client Id"}, "oauth_client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>auth Client Secret"}, "komodo_host_ip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Komodo Host Ip"}, "firewall_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firewall Name"}, "instance_tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Instance Tags"}}, "type": "object", "required": ["package", "cloud_provider", "region", "instance_type", "support_level"], "title": "DeploymentBase"}, "DeploymentCreate": {"properties": {"package": {"type": "string", "enum": ["Pangolin", "Premium"], "title": "Package"}, "cloud_provider": {"type": "string", "title": "Cloud Provider"}, "region": {"type": "string", "title": "Region"}, "instance_type": {"type": "string", "title": "Instance Type"}, "support_level": {"type": "string", "title": "Support Level"}, "komodo_provider_endpoint": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Komodo Provider Endpoint"}, "komodo_api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ko<PERSON><PERSON>"}, "komodo_api_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "github_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Name"}, "domain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Domain"}, "admin_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "admin_username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "admin_password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Admin Password"}, "admin_subdomain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Admin Subdomain"}, "postgres_user": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres User"}, "postgres_password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres Password"}, "postgres_host": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres Host"}, "github_repo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "crowdsec_enrollment_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Crowdsec Enrollment Key"}, "static_page_domain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Static Page Domain"}, "oauth_client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Oauth Client Id"}, "oauth_client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>auth Client Secret"}, "komodo_host_ip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Komodo Host Ip"}, "firewall_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firewall Name"}, "instance_tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Instance Tags"}}, "type": "object", "required": ["package", "cloud_provider", "region", "instance_type", "support_level"], "title": "DeploymentCreate"}, "DeploymentUpdate": {"properties": {"package": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Package"}, "cloud_provider": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cloud Provider"}, "region": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Region"}, "instance_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Instance Type"}, "support_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Support Level"}, "komodo_provider_endpoint": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Komodo Provider Endpoint"}, "komodo_api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ko<PERSON><PERSON>"}, "komodo_api_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "github_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Name"}, "domain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Domain"}, "admin_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "admin_username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "admin_password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Admin Password"}, "admin_subdomain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Admin Subdomain"}, "postgres_user": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres User"}, "postgres_password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres Password"}, "postgres_host": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postgres Host"}, "github_repo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "crowdsec_enrollment_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Crowdsec Enrollment Key"}, "static_page_domain": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Static Page Domain"}, "oauth_client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Oauth Client Id"}, "oauth_client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>auth Client Secret"}, "komodo_host_ip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Komodo Host Ip"}, "firewall_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Firewall Name"}, "instance_tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Instance Tags"}}, "type": "object", "title": "DeploymentUpdate"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "User": {"properties": {"username": {"type": "string", "title": "Username"}, "email": {"type": "string", "title": "Email"}, "id": {"type": "integer", "title": "Id"}}, "type": "object", "required": ["username", "email", "id"], "title": "User"}, "UserCreate": {"properties": {"username": {"type": "string", "title": "Username"}, "email": {"type": "string", "title": "Email"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["username", "email", "password"], "title": "UserCreate"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}