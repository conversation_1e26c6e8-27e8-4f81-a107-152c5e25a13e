Manidae Cloud Frontend Development Plan
Objective
Build a React + TypeScript + Tailwind CSS frontend for a DevOps managed hosting platform that allows technical users to deploy pre-configured open-source software packages on cloud infrastructure.

I would like you to implement a react based front end for a devops based product aimed a a very technical audience. We dont need much marketing or imagery. The functionality is the most important part. React with TypeScript and Tailwind CSS. The goal is to build a simplified managed hosting platform that allows users to deploy pre-configured open-source software packages on cloud infrastructure. The platform will handle infrastructure provisioning, software deployment, and billing. This is the Manidae Cloud managed hosting platform. The user should be able to 
*) Sign up with a username password and email
2) be presented with the option to create a deployment by selecting a server (Pangolin or Premium)
3) be presented with a choice of cloud providers (so far we have aws, google cloud and azure)
4) be  presented with a choice of regions
5) be presented with a choice of instance types (the pricing will be calculated and shown at the side)
6) once the user has selected all the above they will be asked for the required details and the deployment will be created
7) Once created the deployment can be deleted
8) USers can view their deployments and manage them (delete them)
There needs to be some security so that only users can manage their own deployments and their user details.

The backend already exists. Here is an example of the curl requests to the backend

create a deployment (Package Pangolin)

curl -X POST "http://*27.0.0.*:8000/api/deployments/" -H "Content-Type: application/json" -d '{"package": "Pangolin", "cloud_provider": "Google Cloud","region": "us-central*","instance_type": "e2-medium","support_level": "Standard","domain": "contextware.ai","admin_email": "<EMAIL>","admin_username": "<EMAIL>","admin_password": "Password*23q!","admin_subdomain": "pangolin", "client_name": "client*", "client_id": "*", "postgres_host":"pangolin-postgres", "postgres_user":"admin", "postgres_password":"dDuScoEE53vA2Q==", "github_repo":"oidebrett/manidae"}'

create a deployment (Package Premium)

curl -X POST "http://*27.0.0.*:8000/api/deployments/" -H "Content-Type: application/json" -d '{"package": "Premium", "cloud_provider": "Google Cloud","region": "us-central*","instance_type": "e2-medium","support_level": "Standard","domain": "contextware.ai","admin_email": "<EMAIL>","admin_username": "<EMAIL>","admin_password": "Password*23q!","admin_subdomain": "pangolin", "client_name": "client*", "client_id": "*", "postgres_host":"komodo-postgres-*", "postgres_user":"admin", "postgres_password":"dDuScoEE53vA2Q==", "github_repo":"oidebrett/manidae", "static_page_domain":"dashboard", "crowdsec_enrollment_key":"cm9vtmyk3000pjx08brfsa6wd", "oauth_client_id":"252740974698-qjlglbdoh6*6kahvtht5d8flfsog*6hv.apps.googleusercontent.com","oauth_client_secret":"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"}'

to get all deployments
curl "http://*27.0.0.*:8000/api/deployments/"

to get a single deployment
curl "http://*27.0.0.*:8000/api/deployments/*"

to delete a deployment
curl -X DELETE "http://*27.0.0.*:8000/api/deployments/2"

to add a user

curl -X POST "http://*27.0.0.*:8000/api/users/" \
-H "Content-Type: application/json" \
-d '{"username": "testuser", "email": "<EMAIL>", "password": "password"}'

To get all users, use: curl http://*27.0.0.*:8000/api/users/ (note the trailing slash).
To get a single user, use: curl http://*27.0.0.*:8000/api/users/*


Delete a user
curl -X DELETE http://*27.0.0.*:8000/api/users/*

To get the packages
curl -X 'GET' \
  'http://*27.0.0.*:8000/api/packages/' \
  -H 'accept: application/json'
	
Response body
Download
[
  "Pangolin",
  "Premium"
]

To get the cloud providers
curl -X 'GET' \
  'http://*27.0.0.*:8000/api/cloud-providers/' \
  -H 'accept: application/json'

	
Response body
Download
[
  "Google Cloud",
  "AWS",
  "Azure"
]

To get the regions
curl -X 'GET' \
  'http://*27.0.0.*:8000/api/regions/Google%20Cloud' \
  -H 'accept: application/json'

	
Response body
Download
[
  "us-central*",
  "us-east*",
  "europe-west*"
]

To get the instance types
curl -X 'GET' \
  'http://*27.0.0.*:8000/api/instance-types/Google%20Cloud/us-central*' \
  -H 'accept: application/json'

[
  "e2-small",
  "e2-medium",
  "e2-large"
]

To get the costs
curl -X 'POST' \
  'http://*27.0.0.*:8000/api/pricing/calculate' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "package": "Pangolin",
  "cloud_provider": "string",
  "region": "string",
  "instance_type": "string",
  "support_level": "string",
  "komodo_provider_endpoint": "string",
  "komodo_api_key": "string",
  "komodo_api_secret": "string",
  "github_token": "string",
  "client_id": "string",
  "client_name": "string",
  "domain": "string",
  "admin_email": "string",
  "admin_username": "string",
  "admin_password": "string",
  "admin_subdomain": "string",
  "postgres_user": "string",
  "postgres_password": "string",
  "postgres_host": "string",
  "github_repo": "string",
  "crowdsec_enrollment_key": "string",
  "static_page_domain": "string",
  "oauth_client_id": "string",
  "oauth_client_secret": "string",
  "komodo_host_ip": "string",
  "firewall_name": "string",
  "instance_tags": [
    "string"
  ]
}'

	
Response body
Download
{
  "cost": 0
}

also I have included the openapi specification in @docs/openapi.json

I have also attached some screenshots of a very similar service called https://elest.io/ that I want the functionality to be similar to. The screenshots are in the docs/images folder.


Key Requirements Analysis
User Journey
*. Authentication Flow: Sign up/login with username, password, email

2. Deployment Creation: Multi-step wizard (Package → Cloud Provider → Region → Instance Type → Configuration → Deploy)

3. Deployment Management: View all user deployments, delete deployments

4. Security: User-specific access control (users only see/manage their own deployments)

Technical Stack
Frontend: React *8+ with TypeScript
Styling: Tailwind CSS (technical, minimal design)
API Integration: RESTful API at http://*27.0.0.*:8000/api/
State Management: React Context/useState for form state, React Query for server state
Routing: React Router for SPA navigation
STEPs Implementation Plan
[ ] STEP *: Project Setup and Architecture → Web Development STEP
Initialize React project with TypeScript and Tailwind CSS
Set up project structure with proper folder organization
Configure API client with axios/fetch
Set up routing structure
Implement base layout components (Header, Sidebar, Main content area)
[ ] STEP 2: Authentication System → Web Development STEP
Create login/signup forms with proper validation
Implement user authentication state management
Create protected route wrapper
Integrate with backend user endpoints (POST /api/users/, authentication flow)
Add user session persistence (localStorage/sessionStorage)
[ ] STEP 3: Deployment Creation Wizard → Web Development STEP
Step *: Package Selection Component (Pangolin vs Premium cards)
Step 2: Cloud Provider Selection (AWS, Google Cloud, Azure)
Step 3: Region Selection (dynamic based on cloud provider)
Step 4: Instance Type Selection (dynamic based on provider/region)
Step 5: Configuration Form (all required deployment details)
Real-time pricing calculator sidebar component
Progress indicator component
Form state management across wizard steps
API integration for all selection endpoints
[ ] STEP 4: Deployment Management Interface → Web Development STEP
Deployments list/table component with user's deployments
Deployment detail view/modal
Delete deployment functionality with confirmation
Integrate with GET /api/deployments/ and DELETE endpoints
Implement proper error handling and loading states
[ ] STEP 5: UI/UX Polish and Testing → Web Development STEP
Responsive design implementation
Technical-focused styling (clean, functional, minimal imagery)
Error handling and user feedback systems
Loading states and skeleton screens
Form validation and user input feedback
Cross-browser testing and accessibility improvements
API Integration Requirements
Key Endpoints to Integrate:
POST /api/users/ - User registration
GET /api/packages/ - Get available packages (Pangolin, Premium)
GET /api/cloud-providers/ - Get cloud providers
GET /api/regions/{provider} - Get regions for provider
GET /api/instance-types/{provider}/{region} - Get instance types
POST /api/pricing/calculate - Calculate deployment cost
POST /api/deployments/ - Create deployment
GET /api/deployments/ - Get user deployments
DELETE /api/deployments/{id} - Delete deployment
Data Models (from OpenAPI):
User: {username, email, id}
Deployment: {package, cloud_provider, region, instance_type, support_level, ...many optional fields}
DeploymentCreate: All required + optional deployment fields
Design Patterns (based on elest.io analysis)
Layout Structure:
Sidebar Navigation: Project selector, main nav links, billing info
Main Content: Multi-step wizards, tabbed interfaces, card grids
Right Panel: Real-time pricing summary, deployment details
UI Components:
Progress Indicators: Numbered steps with current step highlighting
Card-based Selection: Service cards with icons, descriptions, select buttons
Table Views: Deployment management with actions
Form Wizards: Multi-step forms with navigation
Real-time Updates: Pricing calculator, dynamic dropdowns
Color Scheme (Technical/Professional):
Primary: Orange/amber for CTAs and active states
Secondary: Gray scale for text and borders
Background: Clean whites and light grays
Success/Error: Standard green/red for status indicators
Security Considerations
User authentication state management
API request authentication (likely via headers/tokens)
User-specific data filtering (deployments by user_id)
Input validation and sanitization
Secure storage of user session data
Deliverable
Complete React application with:

Functional user authentication
Multi-step deployment creation wizard
Deployment management interface
Responsive, technical-focused design
Full API integration
Production-ready code structure
Success Criteria
 Users can register and login successfully
 Users can complete full deployment creation flow
 Users can view and delete their deployments only
 Real-time pricing updates work correctly
 All API endpoints integrated properly
 Responsive design works on desktop and mobile
 Clean, technical UI appropriate for DevOps audience