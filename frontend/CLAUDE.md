# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Manidae Cloud** is a DevOps managed hosting platform built with React + TypeScript + Tailwind CSS. It targets technical users who want to deploy pre-configured open-source software packages on cloud infrastructure with minimal setup.

## Core Application Architecture

### Technology Stack
- **Frontend:** React 19.1.0 with TypeScript 4.9.5
- **Build Tool:** Vite 5.4.0 (migrated from Create React App)
- **Styling:** Tailwind CSS 4.1.11
- **Routing:** React Router DOM 7.6.3
- **Testing:** Vitest with React Testing Library

### Application Flow
1. **Authentication:** User registration/login with username, email, password
2. **Package Selection:** Choose between "Pangolin" and "Premium" packages
3. **Multi-step Deployment Wizard:**
   - Cloud provider selection (AWS, Google Cloud, Azure)
   - Region selection
   - Instance type selection with real-time pricing
   - Configuration details input
4. **Deployment Management:** View, manage, and delete user's deployments

### Backend Integration
- **API Base URL:** `http://127.0.0.1:8000/api/`
- **Backend Status:** ✅ Already built and running
- **Key Endpoints:** `/users/`, `/deployments/`, `/packages/`, `/cloud-providers/`, `/regions/`, `/instance-types/`, `/pricing/`
- **Authentication:** User-specific security (users only manage their own deployments)

### ⚠️ IMPORTANT API USAGE RESTRICTIONS
- **DO NOT make real POST requests to `/api/deployments/`** - This creates actual cloud infrastructure and costs money
- **Mock deployment creation** in the frontend instead of calling the real API
- **All other API endpoints** can be called normally (GET requests, user management, etc.)

## Development Commands

### Core Commands
```bash
# Start development server (runs on port 3000)
npm start

# Build for production (includes TypeScript compilation)
npm run build

# Run tests with Vitest
npm test

# Preview production build
npm run serve
```

### Project Structure
```
src/
├── pages/           # Main route components
│   ├── Home.tsx           # Landing page
│   ├── Login.tsx          # User authentication
│   ├── SignUp.tsx         # User registration
│   ├── Dashboard.tsx      # Deployment management
│   └── CreateDeployment.tsx # Multi-step deployment wizard
├── components/      # Reusable UI components (empty - needs implementation)
├── api/            # API client utilities (empty - needs implementation)
├── contexts/       # React contexts for state management (empty - needs implementation)
├── hooks/          # Custom React hooks (empty - needs implementation)
└── App.tsx         # Main app with routing
```

## Key Implementation Notes

### Package Types
- **Pangolin:** Basic deployment package
- **Premium:** Advanced deployment package with additional features (OAuth, CrowdSec, static pages)

### Required Form Fields by Package
**Pangolin Package:**
- package, cloud_provider, region, instance_type, support_level, domain, admin_email, admin_username, admin_password, admin_subdomain, client_name, client_id, postgres_host, postgres_user, postgres_password, github_repo

**Premium Package:**
- All Pangolin fields plus: static_page_domain, crowdsec_enrollment_key, oauth_client_id, oauth_client_secret

### Current Implementation Status
- ✅ Basic routing structure implemented
- ✅ Page components exist (mostly placeholders)
- ✅ Backend API available at `http://127.0.0.1:8000/api/`
- ❌ API integration not implemented
- ❌ Authentication system not implemented
- ❌ Multi-step deployment wizard not implemented
- ❌ Real-time pricing calculator not implemented
- ❌ State management not implemented
- ❌ Deployment creation mocking not implemented

## Architecture Decisions

### State Management
The application will need centralized state management for:
- User authentication state
- Multi-step form state in deployment wizard
- Deployment list state
- Real-time pricing calculations

### API Client Pattern
Create a centralized API client in `src/api/` to handle all backend communication with proper error handling and TypeScript types.

**Special handling for deployment creation:**
- Mock POST `/api/deployments/` requests in the frontend
- Return realistic mock responses for successful deployment creation
- Do not make actual API calls that would provision real cloud infrastructure

### Form Validation
Implement comprehensive form validation for the deployment wizard, especially for required fields that differ between package types.

### Security Implementation
- User-specific data isolation
- Protected routes requiring authentication
- Secure handling of sensitive deployment configuration

## Development Priorities

1. **API Integration:** Implement API client and data fetching
2. **Authentication System:** Login/signup with session management
3. **Multi-step Deployment Wizard:** Complex form with conditional fields
4. **Real-time Pricing:** Dynamic cost calculation based on selections
5. **State Management:** Context API or Redux for global state
6. **Error Handling:** Comprehensive error handling patterns

## Testing Strategy

Use Vitest and React Testing Library for:
- Component unit tests
- Form validation tests
- API integration tests
- User flow tests

The project currently has minimal test coverage and needs comprehensive testing implementation.