server {
  listen 80;
  server_name localhost;

  # Root directory for static files
  root   /usr/share/nginx/html;
  index  index.html;

  # Handle client-side routing for SPAs
  location / {
    try_files $uri $uri/ /index.html;
  }

  # Proxy API requests to the backend service
  # This allows the frontend to call `/api/...`
  # without worrying about CORS or the backend's address.
  location /api {
    proxy_pass http://manidae-backend:8000; # The service name from docker-compose
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
  }
}
