{"version": "0.2.0", "configurations": [{"name": "FastAPI (Uvicorn)", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["app.main:app", "--reload", "--host", "127.0.0.1", "--port", "8000"], "jinja": true, "justMyCode": true, "console": "integratedTerminal", "cwd": "${workspaceFolder}/backend", "envFile": "${workspaceFolder}/.env", "pythonPath": "${workspaceFolder}/backend/venv/bin/python"}]}