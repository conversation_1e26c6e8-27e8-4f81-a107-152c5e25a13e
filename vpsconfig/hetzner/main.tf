# Terraform configuration for Hetzner Cloud
terraform {
  required_providers {
    hcloud = {
      source = "hetznercloud/hcloud"
      version = "~> 1.0"
    }
  }
}

# Configure the Hetzner Cloud Provider
provider "hcloud" {
  token = var.hcloud_token
}

# Conditionally create SSH key resource if user provided one
resource "hcloud_ssh_key" "default" {
  count      = var.use_user_ssh_key ? 1 : 0
  name       = "terraform-key-${var.client_name_lower}"
  public_key = var.ssh_public_key
}

# Reference existing shared SSH key if user didn't provide one
data "hcloud_ssh_key" "shared" {
  count = var.use_user_ssh_key ? 0 : 1
  name  = "terraform-key"
}

# Create a server
resource "hcloud_server" "main" {
  name        = var.instance_name
  image       = var.os_type
  server_type = var.server_type
  location    = var.location
  ssh_keys    = var.use_user_ssh_key ? [hcloud_ssh_key.default[0].id] : [data.hcloud_ssh_key.shared[0].id]
  user_data   = file("${path.module}/startup-script.sh")

  labels = {
    client = var.client_name_lower
  }
}

# Create a firewall if specified
resource "hcloud_firewall" "main" {
  count = var.create_firewall ? 1 : 0
  name  = "firewall-${var.client_name_lower}"

  # Allow SSH
  rule {
    direction  = "in"
    protocol   = "tcp"
    port       = "22"
    source_ips = var.allowed_source_ips
  }

  # Allow HTTP
  rule {
    direction  = "in"
    protocol   = "tcp"
    port       = "80"
    source_ips = var.allowed_source_ips
  }

  # Allow HTTPS
  rule {
    direction  = "in"
    protocol   = "tcp"
    port       = "443"
    source_ips = var.allowed_source_ips
  }

  # Allow Komodo API
  rule {
    direction  = "in"
    protocol   = "tcp"
    port       = "8120"
    source_ips = var.allowed_source_ips
  }

  # Allow Komodo UI
  rule {
    direction  = "in"
    protocol   = "tcp"
    port       = "9120"
    source_ips = var.allowed_source_ips
  }

  # Allow Newt Tunnel
  rule {
    direction  = "in"
    protocol   = "udp"
    port       = "51820"
    source_ips = var.allowed_source_ips
  }

  # Apply to the server
  apply_to {
    server = hcloud_server.main.id
  }
}
