





[[stack]]
name = "test-414347_setup-stack"
[stack.config]
server = "server-test-414347"
repo = "oidebrett/manidae"
reclone = true
file_paths = ["docker-compose-setup.yml"]
environment = """
DOMAIN=example.com

EMAIL=<EMAIL>
ADMIN_USERNAME=None
ADMIN_PASSWORD=None
ADMIN_SUBDOMAIN=pangolin

COMPONENTS="pangolin"
SETUP_TOKEN=1234
MAX_BACKUPS=0
"""
[[stack]]
name = "test-414347_main-stack"
[stack.config]
server = "server-test-414347"
files_on_host = true
reclone = true
run_directory = "/etc/komodo/stacks/test-414347_setup-stack"
post_deploy.command = """
# Add multiple commands on new lines. Supports comments.
docker exec test-414347_main-stack-pangolin-1 node -e "const Database = require('better-sqlite3'); const db = new Database('/app/config/db/db.sqlite'); db.prepare('UPDATE setupTokens SET token = ? WHERE rowid = (SELECT rowid FROM setupTokens ORDER BY rowid LIMIT 1)').run('1234');"
"""
[[procedure]]
name = "test-414347_ProcedureApply"
description = "This procedure runs the initial setup that write out a compose file for the main stack deployment"

[[procedure.config.stage]]
name = "test-414347_Setup"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "test-414347_setup-stack", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "Wait For Compose Write"
enabled = true
executions = [
  { execution.type = "Sleep", execution.params.duration_ms = 10000, enabled = true }
]

[[procedure.config.stage]]
name = "Destroy test-414347_Setup"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "test-414347_setup-stack", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "test-414347_Stack"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "test-414347_main-stack", execution.params.services = [], enabled = true }
]

[[procedure]]
name = "test-414347_ProcedureRestart"

[[procedure.config.stage]]
name = "Stop test-414347_Stack"
enabled = true
executions = [
  { execution.type = "StopStack", execution.params.stack = "test-414347_main-stack", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "Wait For Stack Stop"
enabled = true
executions = [
  { execution.type = "Sleep", execution.params.duration_ms = 10000, enabled = true }
]

[[procedure.config.stage]]
name = "Start test-414347_Stack"
enabled = true
executions = [
  { execution.type = "StartStack", execution.params.stack = "test-414347_main-stack", execution.params.services = [], enabled = true }
]




[[procedure]]
name = "test-414347_ProcedureDestroy"

[[procedure.config.stage]]
name = "test-414347_Stack"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "test-414347_main-stack", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]

[[procedure.config.stage]]
name = "test-414347_Setup"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "test-414347_setup-stack", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]


[[user_group]]
name = "test-414347_user_group"
permissions = [
  { target.type = "Server", target.id = "server-test-414347", level = "Write", specific = ["Attach", "Inspect", "Logs", "Processes", "Terminal"] },
  { target.type = "Stack", target.id = "test-414347_setup-stack", level = "Write", specific = ["Inspect", "Logs", "Terminal"] },
  { target.type = "Stack", target.id = "test-414347_main-stack", level = "Write", specific = ["Inspect", "Logs", "Terminal"] }
]