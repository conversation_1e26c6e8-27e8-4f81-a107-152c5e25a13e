# Configuration Guide

This guide explains the key environment variables that need to be configured for the Manidae Cloud platform.

## Database Configuration

### Required
- **`DATABASE_URL`** - PostgreSQL connection string (e.g., `postgresql://user:password@localhost:5432/dbname`)
- **`DB_USER`** - Database username
- **`DB_PASSWORD`** - Database password  
- **`DB_NAME`** - Database name

## Cloud Provider Configuration

### Required for Deployments
- **`KOMODO_PROVIDER_ENDPOINT`** - Your Komodo provider API endpoint
- **`KOMODO_API_KEY`** - API key for Komodo provider authentication
- **`KOMODO_PASSKEY`** - Passkey for Komodo provider access
- **`KOMODO_PROVIDER_IP`** - IP address of your Komodo provider

### Cloud Provider API Keys (Optional - configure only the providers you use)
- **`HCLOUD_TOKEN`** - Hetzner Cloud API token
- **`G<PERSON>_PROJECT_ID`** - Google Cloud Project ID
- **`LINODE_TOKEN`** - Linode API token
- **`VULTR_API_KEY`** - Vultr API key
- **`DIGITALOCEAN_TOKEN`** - DigitalOcean API token
- **`AWS_ACCESS_KEY_ID`** / **`AWS_SECRET_ACCESS_KEY`** - AWS credentials
- **`AZURE_CLIENT_ID`** / **`AZURE_CLIENT_SECRET`** / **`AZURE_TENANT_ID`** - Azure credentials

## Email Configuration

### Required for Email Features
- **`USE_EMAIL`** - Set to `true` to enable email functionality
- **`MAILGUN_API_URL`** - Mailgun API endpoint (e.g., `https://api.mailgun.net/v3/yourdomain.com`)
- **`MAILGUN_API_KEY`** - Your Mailgun API key
- **`EMAIL_FROM`** - From address for emails (e.g., `"Manidae Cloud <<EMAIL>>"`)
- **`SUPPORT_EMAIL`** - Email address for support requests
- **`VERIFICATION_BASE_URL`** - Frontend URL for email verification (e.g., `https://yourdomain.com/verify-email`)
- **`PASSWORD_RESET_BASE_URL`** - Frontend URL for password reset (e.g., `https://yourdomain.com/reset-password`)

## Billing & Credits Configuration

### Billing Settings
- **`MIN_BALANCE_TO_DEPLOY`** - Minimum account balance required to create deployments (default: `0.00`)
- **`BILLING_INTERVAL_MINUTES`** - Billing cycle in minutes (default: `1440` = 24 hours)
- **`BILLING_BYOVPS_DAILY_FEE`** - Daily fee for BYOVPS deployments in EUR (default: `0.12`)

### Signup Credits
- **`SIGNUP_CREDIT_AMOUNT`** - Credit amount given to new users (default: `0.00`)
- **`SIGNUP_CREDIT_EXPIRY_DAYS`** - Days until signup credits expire (default: `30`)

### Referral System
- **`REFERRAL_CREDIT_AMOUNT`** - Credit amount for referred users (default: `5.00`)
- **`REFERRER_CREDIT_AMOUNT`** - Credit amount for referrers (default: `5.00`)
- **`REFERRAL_CREDIT_EXPIRY_DAYS`** - Days until referral credits expire (default: `30`)
- **`REFERRAL_BASE_URL`** - Base URL for referral links (e.g., `https://yourdomain.com`)

## Payment Processing (Optional)

### Stripe Integration
- **`STRIPE_SECRET_KEY`** - Your Stripe secret key for payment processing
- **`STRIPE_WEBHOOK_SECRET`** - Stripe webhook endpoint secret for verification

## Security & Development

### Required
- **`VITE_API_SECRET_KEY`** - Secret key for API authentication (generate a long random string)

### Development/Testing
- **`TERRAFORM_DRY_RUN`** - Set to `true` to mock infrastructure provisioning without real costs
- **`VITE_MOCK_DEPLOYMENT_CREATION`** - Set to `true` in frontend to mock deployment creation

## Frontend Configuration

### Required Frontend Environment Variables (.env in frontend/)
- **`VITE_MIN_BALANCE_TO_DEPLOY`** - Minimum balance display (should match backend)
- **`VITE_REFERRAL_CREDIT_AMOUNT`** - Referral credit display (should match backend)
- **`VITE_REFERRER_CREDIT_AMOUNT`** - Referrer credit display (should match backend)

## Quick Setup Checklist

1. **Database**: Configure PostgreSQL connection
2. **Komodo Provider**: Set up your deployment provider endpoint and credentials
3. **Email**: Configure Mailgun for user communications
4. **Billing**: Set credit amounts and billing intervals
5. **Security**: Generate strong API secret key
6. **Cloud Providers**: Add API keys for cloud providers you want to support
7. **Frontend**: Ensure frontend environment variables match backend settings

## Example Configuration

See `.env.example` files in both `backend/` and `frontend/` directories for complete example configurations with all available options.

## Security Notes

- Never commit `.env` files to version control
- Use strong, unique passwords and API keys
- Regularly rotate API keys and secrets
- Set `TERRAFORM_DRY_RUN=true` during testing to avoid infrastructure costs
- Use separate credentials for development and production environments
