# Comprehensive Testing Guide for Manidae Cloud Deployment Changes

This guide provides step-by-step instructions for testing all the recent changes to the Manidae Cloud deployment system.

## Overview of Changes

1. **Backend Environment Configuration Cleanup** - Removed postgres credentials since SQLite is now used
2. **DNS Validation and SSL Certificate Monitoring** - Added monitoring system for deployment health
3. **Frontend Package Definition Enhancement** - Added hints and tooltips for environment variables
4. **Optional Environment Variables Implementation** - Added support for optional configuration fields
5. **Bandwidth Monitor Container Permissions Fix** - Fixed Docker container permissions for GCP keys

## Prerequisites

1. Ensure you have the latest code changes
2. Set `TERRAFORM_DRY_RUN=true` in `backend/.env` to avoid provisioning real infrastructure
3. Have Docker and Docker Compose installed
4. Have Python 3.8+ installed

## Automated Testing

### Quick Test Run

```bash
# Run the comprehensive test script
python test_deployment_changes.py
```

This script will automatically test:
- ✅ Postgres credentials removal from templates
- ✅ Package definition enhancements with hints
- ✅ Terraform dry run configuration
- ✅ DNS/SSL monitoring service setup
- ✅ Bandwidth monitor container fix
- ✅ API endpoints (if backend is running)

## Manual Testing Steps

### 1. Backend Environment Configuration Testing

#### Test Postgres Credentials Removal
```bash
# Search for postgres credentials in templates (should return no results)
grep -r "POSTGRES_USER\|POSTGRES_PASSWORD\|POSTGRES_HOST" backend/app/core/deployment/terraform_templates/

# Check that TERRAFORM_DRY_RUN is configured
grep "TERRAFORM_DRY_RUN" backend/.env.example
```

#### Test Terraform Dry Run
```bash
# Set dry run mode
echo "TERRAFORM_DRY_RUN=true" >> backend/.env

# Start backend and create a test deployment
docker-compose up -d backend

# Create a test deployment (should generate files but not provision)
curl -X POST "http://localhost:8000/api/deployments/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "package": "Pangolin",
    "cloud_provider": "Google Cloud",
    "region": "us-central1",
    "instance_type": "e2-medium",
    "support_level": "Standard",
    "domain": "test.example.com",
    "admin_email": "<EMAIL>",
    "admin_subdomain": "admin"
  }'
```

### 2. DNS/SSL Monitoring Testing

#### Test Monitoring Service
```bash
# Start the DNS/SSL monitoring service
docker-compose up -d dns_ssl_monitor

# Check logs
docker-compose logs -f dns_ssl_monitor

# Test monitoring API endpoints
curl "http://localhost:8000/api/deployments/dns-ssl-status"
curl "http://localhost:8000/api/deployments/1/dns-ssl-status"
```

#### Test DNS Resolution Check
```bash
# Test DNS resolution manually
python -c "
import requests
response = requests.get('https://dns.google/resolve?name=example.com&type=A')
print(response.json())
"
```

### 3. Frontend Package Definition Testing

#### Test Enhanced Package Data
```bash
# Start frontend
cd frontend && npm start

# Check package data in browser console
# Navigate to deployment creation page
# Inspect package configuration data for hints
```

#### Test Package JSON Structure
```bash
# Verify package files have correct structure
python -c "
import json
with open('backend/config/packages/pangolin+.json') as f:
    data = json.load(f)
    for detail in data['details']:
        for env in detail['required_env']:
            if isinstance(env, dict):
                print(f'{env[\"name\"]}: {env.get(\"hint\", \"No hint\")}')
"
```

### 4. Optional Environment Variables Testing

#### Test Optional Fields in Frontend
1. Navigate to deployment creation page
2. Select "Pangolin+" package
3. Look for "Optional Settings" collapsible sections
4. Verify tooltips appear on hover for fields with hints

#### Test Optional Fields in Backend
```bash
# Create deployment with optional fields
curl -X POST "http://localhost:8000/api/deployments/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "package": "Pangolin+",
    "cloud_provider": "Google Cloud",
    "region": "us-central1",
    "instance_type": "e2-medium",
    "support_level": "Standard",
    "domain": "test.example.com",
    "admin_email": "<EMAIL>",
    "admin_subdomain": "admin",
    "traefik_subdomain": "traefik-custom",
    "logs_subdomain": "logs-custom"
  }'
```

### 5. Bandwidth Monitor Testing

#### Test Container Permissions
```bash
# Start bandwidth monitor
docker-compose up -d bandwidth_monitor

# Check logs for permission errors
docker-compose logs bandwidth_monitor

# Should not see "EACCES: permission denied" errors
```

#### Test GCP Credentials Path
```bash
# Verify correct environment file is used
docker-compose exec bandwidth_monitor env | grep GCP_CREDENTIALS_FILE
# Should show: GCP_CREDENTIALS_FILE=/app/keys/your-service-account-key.json
```

## Integration Testing Scenarios

### Scenario 1: Complete Deployment Flow
1. Start all services with `docker-compose up -d`
2. Create user account via frontend
3. Navigate through deployment creation wizard
4. Fill in required fields (observe hints)
5. Expand optional settings and fill some fields
6. Submit deployment (should work in dry run mode)
7. Monitor deployment status
8. Check DNS/SSL monitoring for the deployment

### Scenario 2: Package Configuration Testing
1. Test each package type (Pangolin, Pangolin+, Pangolin+AI)
2. Verify all required fields have hints
3. Verify optional fields are properly hidden/expandable
4. Test form validation with and without optional fields

### Scenario 3: Monitoring System Testing
1. Create active deployment (in dry run mode)
2. Test DNS/SSL monitoring endpoints
3. Simulate DNS issues and verify detection
4. Test deployment restart functionality

## Expected Results

### ✅ Success Criteria
- No postgres credentials in terraform templates
- All package definitions have hints for required fields
- Optional environment variables are properly supported
- DNS/SSL monitoring service runs without errors
- Bandwidth monitor container starts without permission errors
- Frontend displays hints and optional fields correctly
- Terraform dry run mode prevents actual infrastructure provisioning

### ❌ Failure Indicators
- Postgres credentials still present in templates
- Missing hints in package definitions
- Optional fields not working in frontend
- Permission denied errors in bandwidth monitor
- DNS/SSL monitoring service failing to start
- Terraform attempting to provision real infrastructure

## Troubleshooting

### Common Issues

1. **Permission Errors in Bandwidth Monitor**
   - Check that `docker-compose.yml` uses `./scripts/.env.docker`
   - Verify GCP credentials path is `/app/keys/` in container

2. **Missing Hints in Frontend**
   - Check package JSON files have correct structure
   - Verify frontend types are updated
   - Clear browser cache and restart frontend

3. **Terraform Dry Run Not Working**
   - Ensure `TERRAFORM_DRY_RUN=true` in backend/.env
   - Check that setting is loaded in config.py
   - Restart backend service

4. **DNS/SSL Monitoring Errors**
   - Check network connectivity
   - Verify deployment has valid IP address
   - Check monitoring service logs

## Cleanup

After testing, clean up resources:

```bash
# Stop all services
docker-compose down

# Remove test data
docker volume prune

# Reset environment variables
git checkout backend/.env
```

## Reporting Issues

If you encounter issues during testing:

1. Run the automated test script: `python test_deployment_changes.py`
2. Collect relevant logs: `docker-compose logs > test-logs.txt`
3. Document the specific test scenario that failed
4. Include error messages and expected vs actual behavior
