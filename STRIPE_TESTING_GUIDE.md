# Stripe Payment Integration Testing Guide

This guide will help you test your Stripe payment integration thoroughly, both with and without actual Stripe webhooks.

## 🚨 Issues Found and Fixed

### 1. Product IDs vs Price IDs
**Problem**: Your `PRICE_MAP` contains Product IDs (`prod_*`) but Stripe checkout needs Price IDs (`price_*`).

**Solution**: Run the helper script to get correct Price IDs:
```bash
python get_stripe_price_ids.py
```

### 2. Webhook Testing on Localhost
**Problem**: Stripe webhooks can't reach `http://localhost` directly.

**Solutions**: Multiple testing approaches provided below.

## 🛠️ Setup Steps

### Step 1: Get Correct Stripe Price IDs

1. **Run the Price ID helper**:
   ```bash
   python get_stripe_price_ids.py
   ```

2. **If you don't have prices yet**, the script can create test prices for you (test mode only).

3. **Copy the generated PRICE_MAP** and replace it in `backend/app/api/stripe_payments.py`.

### Step 2: Verify Environment Variables

Check your `backend/.env` file has:
```env
STRIPE_SECRET_KEY=sk_live_... # or sk_test_... for testing
STRIPE_WEBHOOK_SECRET=whsec_... # Get this from Stripe dashboard
```

## 🧪 Testing Methods

### Method 1: Automated Testing (Recommended)

Run the comprehensive test script:
```bash
python test_stripe_integration.py
```

This script will:
- ✅ Test user authentication
- ✅ Test checkout session creation
- ✅ Test webhook simulation
- ✅ Test full payment flow
- ✅ Test invalid amounts
- ✅ Verify balance updates
- ✅ Check transaction history

### Method 2: Manual Frontend Testing

1. **Start your backend**:
   ```bash
   cd backend
   uvicorn app.main:app --reload
   ```

2. **Start your frontend**:
   ```bash
   cd frontend
   npm run dev
   ```

3. **Test in browser**:
   - Go to http://localhost:3000/billing
   - Try different amounts (€5, €10, €20, €50, €100)
   - Use the "🧪 Test Payment" button for instant testing
   - Use "Add funds with Stripe" for real Stripe checkout

### Method 3: Real Stripe Webhook Testing

#### Option A: Using ngrok (Recommended)

1. **Install ngrok**: https://ngrok.com/download

2. **Start the webhook test server**:
   ```bash
   python webhook_test_server.py
   ```

3. **In another terminal, expose it**:
   ```bash
   ngrok http 5000
   ```

4. **Configure Stripe webhook**:
   - Go to https://dashboard.stripe.com/webhooks
   - Add endpoint: `https://your-ngrok-url.ngrok.io/webhook`
   - Subscribe to: `checkout.session.completed`

5. **Test real payments** using Stripe test cards:
   - `****************` (Visa)
   - `****************` (Declined)

#### Option B: Using the Mock Webhook Server

1. **Start the webhook server**:
   ```bash
   python webhook_test_server.py
   ```

2. **Visit the web interface**: http://localhost:5000

3. **Send test webhooks** using the form or API:
   ```bash
   curl -X POST http://localhost:5000/TBD_test-webhook \
     -H "Content-Type: application/json" \
     -d '{"user_id": 1, "amount_cents": 500}'
   ```

## 🔍 Testing Checklist

### Basic Functionality
- [ ] User can see current balance
- [ ] Checkout session creation works for valid amounts
- [ ] Invalid amounts are rejected
- [ ] Test payment button works
- [ ] Real Stripe checkout redirects properly

### Payment Processing
- [ ] Webhook receives and processes payments
- [ ] User balance updates correctly
- [ ] Transaction records are created
- [ ] Success page shows updated balance
- [ ] Transaction history displays payments

### Error Handling
- [ ] Invalid amounts show proper error
- [ ] Network errors are handled gracefully
- [ ] Stripe errors are displayed to user
- [ ] Webhook signature verification works (if enabled)

### Edge Cases
- [ ] Multiple rapid payments
- [ ] Very large amounts
- [ ] User with zero balance
- [ ] Cancelled payments
- [ ] Failed payments

## 🐛 Common Issues and Solutions

### Issue: "Invalid amount" error
**Cause**: Amount not in PRICE_MAP
**Solution**: Use only €5, €10, €20, €50, €100 or update PRICE_MAP

### Issue: "Stripe error: No such price"
**Cause**: Using Product IDs instead of Price IDs
**Solution**: Run `get_stripe_price_ids.py` and update PRICE_MAP

### Issue: Webhook not received
**Cause**: Localhost not accessible to Stripe
**Solution**: Use ngrok or test endpoints

### Issue: Balance not updating
**Cause**: Webhook processing failed
**Solution**: Check backend logs, verify webhook endpoint

### Issue: "Authentication failed"
**Cause**: Wrong Stripe API key
**Solution**: Verify STRIPE_SECRET_KEY in .env

## 📊 Test Data

### Valid Test Amounts (cents)
- 500 (€5.00)
- 1000 (€10.00)
- 2000 (€20.00)
- 5000 (€50.00)
- 10000 (€100.00)

### Stripe Test Cards
- **Success**: ****************
- **Declined**: ****************
- **Requires 3DS**: ****************

### Test User Credentials
The test script creates a user with:
- Username: testuser
- Password: testpass123
- Email: <EMAIL>

## 🚀 Production Deployment

Before going live:

1. **Replace test keys** with live Stripe keys
2. **Remove test endpoints** (`/test-webhook`, `/simulate-checkout-flow`)
3. **Set up real webhook endpoint** in Stripe dashboard
4. **Enable webhook signature verification**
5. **Test with real (small) amounts**

## 📞 Support

If you encounter issues:

1. **Check the logs** in your backend terminal
2. **Run the test script** to isolate the problem
3. **Verify Stripe dashboard** for webhook delivery status
4. **Check network connectivity** if using ngrok

The testing setup provides multiple ways to verify your integration works correctly before going live!
